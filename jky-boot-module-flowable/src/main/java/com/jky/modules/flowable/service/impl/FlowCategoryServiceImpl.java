package com.jky.modules.flowable.service.impl;

import com.jky.modules.flowable.entity.FlowCategory;
import com.jky.modules.flowable.mapper.FlowCategoryMapper;
import com.jky.modules.flowable.service.IFlowCategoryService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 流程分类表
 * @Author: jky
 * @Date:   2022-10-06
 * @Version: V1.0
 */
@Service
public class FlowCategoryServiceImpl extends ServiceImpl<FlowCategoryMapper, FlowCategory> implements IFlowCategoryService {

}
