CREATE TABLE `dgswr_projects`
(
    `project_id`   varchar(64)  NOT NULL COMMENT '项目ID',
    `project_name` VARCHAR(255) NOT NULL COMMENT '项目名称',
    `start_time`   TIMESTAMP    NOT NULL COMMENT '开始时间',
    `end_time`     TIMESTAMP    NOT NULL COMMENT '结束时间',
    `status`       CHAR(1) COMMENT '状态',
    `remark`       VARCHAR(255) COMMENT '备注',
    `create_time`  TIMESTAMP    NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`  TIMESTAMP    NULL DEFAULT NULL COMMENT '更新时间',
    `create_by`    VARCHAR(64) COMMENT '创建人',
    `update_by`    VARCHAR(64) COMMENT '更新人',
    PRIMARY KEY (`project_id`)
) ENGINE = InnoDB COMMENT ='项目表';

CREATE TABLE `dgswr_workfaces`
(
    `workface_id`   varchar(64) NOT NULL COMMENT '工作面ID',
    `project_id`    varchar(64) COMMENT '项目ID',
    `workface_name` VARCHAR(255) COMMENT '工作面名称',
    `address`       VARCHAR(255) COMMENT '地址',
    `lat`           VARCHAR(255) COMMENT '纬度',
    `lon`           VARCHAR(255) COMMENT '经度',
    `remark`        VARCHAR(255) COMMENT '备注',
    `introduction`  TEXT COMMENT '工作面介绍',
    `details`       TEXT COMMENT '工作面详情',
    `create_time`   TIMESTAMP   NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`   TIMESTAMP   NULL DEFAULT NULL COMMENT '更新时间',
    `create_by`     VARCHAR(64) COMMENT '创建人',
    `update_by`     VARCHAR(64) COMMENT '更新人',
    PRIMARY KEY (`workface_id`)
) ENGINE = InnoDB COMMENT ='工作面表';


CREATE TABLE `dgswr_reports`
(
    `report_id`      varchar(64) NOT NULL COMMENT '上报ID',
    `workface_id`    varchar(64) COMMENT '工作面ID',
    `report_content` VARCHAR(255) COMMENT '上报内容',
    `report_file`    VARCHAR(255) COMMENT '上报文件',
    `report_time`    TIMESTAMP COMMENT '上报时间',
    `report_user_id` BIGINT COMMENT '上报用户ID',
    `report_status`  CHAR(1) COMMENT '上报状态',
    `remark`         VARCHAR(255) COMMENT '备注',
    `create_time`    TIMESTAMP   NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`    TIMESTAMP   NULL DEFAULT NULL COMMENT '更新时间',
    `create_by`      VARCHAR(64) COMMENT '创建人',
    `update_by`      VARCHAR(64) COMMENT '更新人',
    PRIMARY KEY (`report_id`)
) ENGINE = InnoDB COMMENT ='上报内容表';