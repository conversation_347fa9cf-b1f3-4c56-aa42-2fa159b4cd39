-- 更新上报表结构的脚本
-- 执行日期: 2025-06-19
-- 说明: 更新dgswr_reports表结构，添加新字段并修改现有字段

-- 检查表是否存在
SELECT COUNT(*) FROM information_schema.tables 
WHERE table_schema = DATABASE() AND table_name = 'dgswr_reports';

-- 如果表不存在，创建新表
CREATE TABLE IF NOT EXISTS `dgswr_reports`
(
    `report_id`      varchar(64) NOT NULL COMMENT '上报ID',
    `workface_id`    varchar(64) COMMENT '工作面ID',
    `report_content` TEXT COMMENT '上报内容',
    `report_images`  TEXT COMMENT '上报图片（多个图片用逗号分隔）',
    `report_time`    TIMESTAMP COMMENT '上报时间',
    `report_user_id` BIGINT COMMENT '上报用户ID',
    `report_user_name` VARCHAR(100) COMMENT '上报人姓名',
    `report_unit`    VARCHAR(200) COMMENT '上报单位',
    `report_status`  CHAR(1) DEFAULT '1' COMMENT '上报状态（0-草稿，1-已上报）',
    `remark`         VARCHAR(255) COMMENT '备注',
    `create_time`    TIMESTAMP   NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`    TIMESTAMP   NULL DEFAULT NULL COMMENT '更新时间',
    `create_by`      VARCHAR(64) COMMENT '创建人',
    `update_by`      VARCHAR(64) COMMENT '更新人',
    PRIMARY KEY (`report_id`)
) ENGINE = InnoDB COMMENT ='上报内容表';

-- 如果表已存在但结构不同，则添加新字段（如果不存在的话）

-- 添加项目ID字段
ALTER TABLE `dgswr_reports`
ADD COLUMN IF NOT EXISTS `project_id` VARCHAR(64) COMMENT '项目ID' AFTER `report_id`;

-- 添加项目名称字段
ALTER TABLE `dgswr_reports`
ADD COLUMN IF NOT EXISTS `project_name` VARCHAR(255) COMMENT '项目名称' AFTER `project_id`;

-- 添加工作面名称字段
ALTER TABLE `dgswr_reports`
ADD COLUMN IF NOT EXISTS `workface_name` VARCHAR(255) COMMENT '工作面名称' AFTER `workface_id`;

-- 添加工作面介绍字段
ALTER TABLE `dgswr_reports`
ADD COLUMN IF NOT EXISTS `introduction` TEXT COMMENT '工作面介绍' AFTER `workface_name`;

-- 添加工作面详情字段
ALTER TABLE `dgswr_reports`
ADD COLUMN IF NOT EXISTS `details` TEXT COMMENT '工作面详情' AFTER `introduction`;

-- 添加上报人姓名字段
ALTER TABLE `dgswr_reports`
ADD COLUMN IF NOT EXISTS `report_user_name` VARCHAR(100) COMMENT '上报人姓名' AFTER `report_user_id`;

-- 添加上报单位字段
ALTER TABLE `dgswr_reports`
ADD COLUMN IF NOT EXISTS `report_unit` VARCHAR(200) COMMENT '上报单位' AFTER `report_user_name`;

-- 修改上报内容字段类型为TEXT
ALTER TABLE `dgswr_reports`
MODIFY COLUMN `report_content` TEXT COMMENT '上报内容';

-- 添加上报图片字段（如果原来是report_file字段，则重命名）
ALTER TABLE `dgswr_reports`
ADD COLUMN IF NOT EXISTS `report_images` TEXT COMMENT '上报图片（多个图片用逗号分隔）' AFTER `report_content`;

-- 修改上报用户ID字段类型
ALTER TABLE `dgswr_reports`
MODIFY COLUMN `report_user_id` BIGINT COMMENT '上报用户ID';

-- 修改上报状态字段默认值
ALTER TABLE `dgswr_reports`
MODIFY COLUMN `report_status` CHAR(1) DEFAULT '1' COMMENT '上报状态（0-草稿，1-已上报）';

-- 验证表结构
DESCRIBE `dgswr_reports`;
