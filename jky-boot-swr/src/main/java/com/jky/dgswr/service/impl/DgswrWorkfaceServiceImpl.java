package com.jky.dgswr.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jky.dgswr.domain.DgswrWorkface;
import com.jky.dgswr.domain.bo.DgswrWorkfaceBo;
import com.jky.dgswr.domain.vo.DgswrWorkfaceVo;
import com.jky.dgswr.domain.vo.DgswrProjectVo;
import com.jky.dgswr.mapper.DgswrWorkfaceMapper;
import com.jky.dgswr.service.IDgswrWorkfaceService;
import com.jky.dgswr.service.IDgswrProjectService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 工作面管理Service实现类
 */
@Service
@RequiredArgsConstructor
public class DgswrWorkfaceServiceImpl implements IDgswrWorkfaceService {

    private final DgswrWorkfaceMapper dgswrWorkfaceMapper;
    private final IDgswrProjectService dgswrProjectService;

    @Override
    public List<DgswrWorkfaceVo> selectWorkfaceList(DgswrWorkfaceBo query) {
        LambdaQueryWrapper<DgswrWorkface> wrapper = Wrappers.<DgswrWorkface>lambdaQuery();
        
        // 根据项目ID查询
        if (StringUtils.hasText(query.getProjectId())) {
            wrapper.eq(DgswrWorkface::getProjectId, query.getProjectId());
        }
        
        // 根据工作面名称模糊查询
        if (StringUtils.hasText(query.getWorkfaceName())) {
            wrapper.like(DgswrWorkface::getWorkfaceName, query.getWorkfaceName());
        }
        
        // 根据地址模糊查询
        if (StringUtils.hasText(query.getAddress())) {
            wrapper.like(DgswrWorkface::getAddress, query.getAddress());
        }
        
        List<DgswrWorkface> list = dgswrWorkfaceMapper.selectList(wrapper);
        
        return list.stream().map(this::convertToVo).collect(Collectors.toList());
    }

    @Override
    public DgswrWorkfaceVo queryById(String workfaceId) {
        DgswrWorkface workface = dgswrWorkfaceMapper.selectById(workfaceId);
        if (workface == null) {
            return null;
        }
        DgswrWorkfaceVo vo = convertToVo(workface);

        // 查询项目名称
        if (StringUtils.hasText(workface.getProjectId())) {
            DgswrProjectVo project = dgswrProjectService.queryById(workface.getProjectId());
            if (project != null) {
                vo.setProjectName(project.getProjectName());
            }
        }

        return vo;
    }

    @Override
    public Boolean insert(DgswrWorkfaceBo bo) {
        DgswrWorkface workface = new DgswrWorkface();
        BeanUtils.copyProperties(bo, workface);
        
        // 生成工作面ID
        if (!StringUtils.hasText(workface.getWorkfaceId())) {
            workface.setWorkfaceId(UUID.randomUUID().toString().replace("-", ""));
        }
        
        int result = dgswrWorkfaceMapper.insert(workface);
        return result > 0;
    }

    @Override
    public Boolean update(DgswrWorkfaceBo bo) {
        DgswrWorkface workface = new DgswrWorkface();
        BeanUtils.copyProperties(bo, workface);
        
        int result = dgswrWorkfaceMapper.updateById(workface);
        return result > 0;
    }

    @Override
    public Boolean deleteById(String workfaceId) {
        int result = dgswrWorkfaceMapper.deleteById(workfaceId);
        return result > 0;
    }

    @Override
    public List<DgswrWorkfaceVo> selectWorkfaceListByProjectId(String projectId) {
        LambdaQueryWrapper<DgswrWorkface> wrapper = Wrappers.<DgswrWorkface>lambdaQuery();
        wrapper.eq(DgswrWorkface::getProjectId, projectId);
        
        List<DgswrWorkface> list = dgswrWorkfaceMapper.selectList(wrapper);
        
        return list.stream().map(this::convertToVo).collect(Collectors.toList());
    }

    /**
     * 实体转换为VO
     */
    private DgswrWorkfaceVo convertToVo(DgswrWorkface workface) {
        DgswrWorkfaceVo vo = new DgswrWorkfaceVo();
        BeanUtils.copyProperties(workface, vo);
        return vo;
    }
}
