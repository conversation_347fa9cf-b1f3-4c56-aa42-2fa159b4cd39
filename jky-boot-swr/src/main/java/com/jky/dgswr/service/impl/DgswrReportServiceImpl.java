package com.jky.dgswr.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jky.dgswr.domain.DgswrReport;
import com.jky.dgswr.domain.bo.DgswrReportBo;
import com.jky.dgswr.domain.bo.DgswrWorkfaceBo;
import com.jky.dgswr.domain.vo.DgswrReportVo;
import com.jky.dgswr.domain.vo.DgswrWorkfaceReportVo;
import com.jky.dgswr.domain.vo.DgswrWorkfaceVo;
import com.jky.dgswr.mapper.DgswrReportMapper;
import com.jky.dgswr.service.IDgswrReportService;
import com.jky.dgswr.service.IDgswrWorkfaceService;
import lombok.RequiredArgsConstructor;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.system.vo.LoginUser;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 上报内容管理Service实现类
 */
@Service
@RequiredArgsConstructor
public class DgswrReportServiceImpl implements IDgswrReportService {

    private final DgswrReportMapper dgswrReportMapper;
    private final IDgswrWorkfaceService dgswrWorkfaceService;

    @Override
    public List<DgswrReportVo> selectReportList(DgswrReportBo query) {
        LambdaQueryWrapper<DgswrReport> wrapper = Wrappers.<DgswrReport>lambdaQuery();

        // 根据工作面ID查询
        if (StringUtils.hasText(query.getWorkfaceId())) {
            wrapper.eq(DgswrReport::getWorkfaceId, query.getWorkfaceId());
        }

        // 根据上报状态查询
        if (StringUtils.hasText(query.getReportStatus())) {
            wrapper.eq(DgswrReport::getReportStatus, query.getReportStatus());
        }

        // 根据上报时间范围查询
        if (query.getReportStartDate() != null) {
            wrapper.ge(DgswrReport::getReportTime, query.getReportStartDate());
        }
        if (query.getReportEndDate() != null) {
            // 结束日期加一天，包含当天
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(query.getReportEndDate());
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            wrapper.lt(DgswrReport::getReportTime, calendar.getTime());
        }

        wrapper.orderByDesc(DgswrReport::getReportTime);

        List<DgswrReport> list = dgswrReportMapper.selectList(wrapper);

        return list.stream().map(this::convertToVo).collect(Collectors.toList());
    }

    @Override
    public DgswrReportVo queryById(String reportId) {
        DgswrReport report = dgswrReportMapper.selectById(reportId);
        if (report == null) {
            return null;
        }
        return convertToVo(report);
    }

    @Override
    public Boolean insert(DgswrReportBo bo) {
        DgswrReport report = new DgswrReport();
        BeanUtils.copyProperties(bo, report);

        report.setReportTime(new Date());
        if (!StringUtils.hasText(report.getReportStatus())) {
            report.setReportStatus("1");
        }
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        report.setReportUserId(loginUser.getUsername());
        if (StringUtils.hasText(report.getWorkfaceId())) {
            DgswrWorkfaceVo workface = dgswrWorkfaceService.queryById(report.getWorkfaceId());
            if (workface != null) {
                report.setProjectId(workface.getProjectId());
                report.setProjectName(workface.getProjectName());
                report.setWorkfaceName(workface.getWorkfaceName());
                report.setIntroduction(workface.getIntroduction());
                report.setDetails(workface.getDetails());
            }
        }
        int result = dgswrReportMapper.insert(report);
        return result > 0;
    }

    @Override
    public Boolean update(DgswrReportBo bo) {
        DgswrReport report = new DgswrReport();
        BeanUtils.copyProperties(bo, report);

        int result = dgswrReportMapper.updateById(report);
        return result > 0;
    }

    @Override
    public Boolean deleteById(String reportId) {
        int result = dgswrReportMapper.deleteById(reportId);
        return result > 0;
    }

    @Override
    public List<DgswrWorkfaceReportVo> selectWorkfaceReportList(DgswrReportBo query) {
        // 先查询工作面列表
        DgswrWorkfaceBo workfaceQuery = new DgswrWorkfaceBo();
        if (StringUtils.hasText(query.getWorkfaceName())) {
            workfaceQuery.setWorkfaceName(query.getWorkfaceName());
        }
        if (StringUtils.hasText(query.getProjectName())) {
            // 这里需要通过项目名称查询项目ID，然后设置到工作面查询条件中
            // 暂时先不实现，可以后续优化
        }

        List<DgswrWorkfaceVo> workfaceList = dgswrWorkfaceService.selectWorkfaceList(workfaceQuery);

        List<DgswrWorkfaceReportVo> resultList = new ArrayList<>();

        for (DgswrWorkfaceVo workface : workfaceList) {
            // 查询该工作面的最新上报记录
            LambdaQueryWrapper<DgswrReport> wrapper = Wrappers.<DgswrReport>lambdaQuery();
            wrapper.eq(DgswrReport::getWorkfaceId, workface.getWorkfaceId());

            // 根据上报时间范围查询
            if (query.getReportStartDate() != null) {
                wrapper.ge(DgswrReport::getReportTime, query.getReportStartDate());
            }
            if (query.getReportEndDate() != null) {
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(query.getReportEndDate());
                calendar.add(Calendar.DAY_OF_MONTH, 1);
                wrapper.lt(DgswrReport::getReportTime, calendar.getTime());
            }

            wrapper.orderByDesc(DgswrReport::getReportTime);
            wrapper.last("LIMIT 1"); // 只取最新的一条记录

            DgswrReport latestReport = dgswrReportMapper.selectOne(wrapper);

            DgswrWorkfaceReportVo reportVo = new DgswrWorkfaceReportVo();

            // 设置工作面基本信息
            reportVo.setWorkfaceId(workface.getWorkfaceId());
            reportVo.setWorkfaceName(workface.getWorkfaceName());
            reportVo.setProjectId(workface.getProjectId());
            reportVo.setProjectName(workface.getProjectName());
            reportVo.setAddress(workface.getAddress());
            reportVo.setIntroduction(workface.getIntroduction());
            reportVo.setDetails(workface.getDetails());
            reportVo.setRemark(workface.getRemark());

            if (latestReport != null) {
                // 有上报记录
                reportVo.setReportContent(latestReport.getReportContent());
                reportVo.setReportImages(latestReport.getReportImages());
                reportVo.setReportStatus(latestReport.getReportStatus());
                reportVo.setReportStatusName("已上报");
                reportVo.setReportUserName(latestReport.getReportUserName());
                reportVo.setReportUnit(latestReport.getReportUnit());
                reportVo.setLatestReportTime(latestReport.getReportTime());
                reportVo.setLatestReportId(latestReport.getReportId());

                // 如果上报记录中有项目和工作面信息，优先使用上报记录中的信息
                if (StringUtils.hasText(latestReport.getProjectName())) {
                    reportVo.setProjectName(latestReport.getProjectName());
                }
                if (StringUtils.hasText(latestReport.getWorkfaceName())) {
                    reportVo.setWorkfaceName(latestReport.getWorkfaceName());
                }
                if (StringUtils.hasText(latestReport.getIntroduction())) {
                    reportVo.setIntroduction(latestReport.getIntroduction());
                }
                if (StringUtils.hasText(latestReport.getDetails())) {
                    reportVo.setDetails(latestReport.getDetails());
                }
            } else {
                // 无上报记录
                reportVo.setReportStatus("0");
                reportVo.setReportStatusName("未上报");
            }

            resultList.add(reportVo);
        }

        return resultList;
    }

    /**
     * 实体转换为VO
     */
    private DgswrReportVo convertToVo(DgswrReport report) {
        DgswrReportVo vo = new DgswrReportVo();
        BeanUtils.copyProperties(report, vo);

        // 设置状态名称
        if ("1".equals(report.getReportStatus())) {
            vo.setReportStatusName("已上报");
        } else {
            vo.setReportStatusName("草稿");
        }

        return vo;
    }
}
