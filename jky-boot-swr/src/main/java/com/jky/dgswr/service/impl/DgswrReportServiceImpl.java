package com.jky.dgswr.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jky.dgswr.domain.DgswrReport;
import com.jky.dgswr.domain.bo.DgswrReportBo;
import com.jky.dgswr.domain.bo.DgswrWorkfaceBo;
import com.jky.dgswr.domain.vo.DgswrReportVo;
import com.jky.dgswr.domain.vo.DgswrWorkfaceVo;
import com.jky.dgswr.mapper.DgswrReportMapper;
import com.jky.dgswr.service.IDgswrReportService;
import com.jky.dgswr.service.IDgswrWorkfaceService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 上报内容管理Service实现类
 */
@Service
@RequiredArgsConstructor
public class DgswrReportServiceImpl implements IDgswrReportService {

    private final DgswrReportMapper dgswrReportMapper;
    private final IDgswrWorkfaceService dgswrWorkfaceService;

    @Override
    public List<DgswrReportVo> selectReportList(DgswrReportBo query) {
        LambdaQueryWrapper<DgswrReport> wrapper = Wrappers.<DgswrReport>lambdaQuery();
        
        // 根据工作面ID查询
        if (StringUtils.hasText(query.getWorkfaceId())) {
            wrapper.eq(DgswrReport::getWorkfaceId, query.getWorkfaceId());
        }
        
        // 根据上报状态查询
        if (StringUtils.hasText(query.getReportStatus())) {
            wrapper.eq(DgswrReport::getReportStatus, query.getReportStatus());
        }
        
        // 根据上报时间范围查询
        if (query.getReportStartDate() != null) {
            wrapper.ge(DgswrReport::getReportTime, query.getReportStartDate());
        }
        if (query.getReportEndDate() != null) {
            // 结束日期加一天，包含当天
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(query.getReportEndDate());
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            wrapper.lt(DgswrReport::getReportTime, calendar.getTime());
        }
        
        wrapper.orderByDesc(DgswrReport::getReportTime);
        
        List<DgswrReport> list = dgswrReportMapper.selectList(wrapper);
        
        return list.stream().map(this::convertToVo).collect(Collectors.toList());
    }

    @Override
    public DgswrReportVo queryById(String reportId) {
        DgswrReport report = dgswrReportMapper.selectById(reportId);
        if (report == null) {
            return null;
        }
        return convertToVo(report);
    }

    @Override
    public Boolean insert(DgswrReportBo bo) {
        DgswrReport report = new DgswrReport();
        BeanUtils.copyProperties(bo, report);
        
        // 生成上报ID
        if (!StringUtils.hasText(report.getReportId())) {
            report.setReportId(UUID.randomUUID().toString().replace("-", ""));
        }
        
        // 设置上报时间
        if (report.getReportTime() == null) {
            report.setReportTime(new Date());
        }
        
        // 默认状态为已上报
        if (!StringUtils.hasText(report.getReportStatus())) {
            report.setReportStatus("1");
        }
        
        int result = dgswrReportMapper.insert(report);
        return result > 0;
    }

    @Override
    public Boolean update(DgswrReportBo bo) {
        DgswrReport report = new DgswrReport();
        BeanUtils.copyProperties(bo, report);
        
        int result = dgswrReportMapper.updateById(report);
        return result > 0;
    }

    @Override
    public Boolean deleteById(String reportId) {
        int result = dgswrReportMapper.deleteById(reportId);
        return result > 0;
    }

    @Override
    public List<DgswrReportVo> selectWorkfaceReportList(DgswrReportBo query) {
        // 先查询工作面列表
        DgswrWorkfaceBo workfaceQuery = new DgswrWorkfaceBo();
        if (StringUtils.hasText(query.getWorkfaceName())) {
            workfaceQuery.setWorkfaceName(query.getWorkfaceName());
        }
        if (StringUtils.hasText(query.getProjectName())) {
            // 这里需要通过项目名称查询项目ID，然后设置到工作面查询条件中
            // 暂时先不实现，可以后续优化
        }
        
        List<DgswrWorkfaceVo> workfaceList = dgswrWorkfaceService.selectWorkfaceList(workfaceQuery);
        
        List<DgswrReportVo> resultList = new ArrayList<>();
        
        for (DgswrWorkfaceVo workface : workfaceList) {
            // 查询该工作面的最新上报记录
            LambdaQueryWrapper<DgswrReport> wrapper = Wrappers.<DgswrReport>lambdaQuery();
            wrapper.eq(DgswrReport::getWorkfaceId, workface.getWorkfaceId());
            
            // 根据上报时间范围查询
            if (query.getReportStartDate() != null) {
                wrapper.ge(DgswrReport::getReportTime, query.getReportStartDate());
            }
            if (query.getReportEndDate() != null) {
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(query.getReportEndDate());
                calendar.add(Calendar.DAY_OF_MONTH, 1);
                wrapper.lt(DgswrReport::getReportTime, calendar.getTime());
            }
            
            wrapper.orderByDesc(DgswrReport::getReportTime);
            wrapper.last("LIMIT 1"); // 只取最新的一条记录
            
            DgswrReport latestReport = dgswrReportMapper.selectOne(wrapper);
            
            DgswrReportVo reportVo = new DgswrReportVo();
            
            if (latestReport != null) {
                // 有上报记录
                BeanUtils.copyProperties(latestReport, reportVo);
                reportVo.setReportStatusName("已上报");
            } else {
                // 无上报记录
                reportVo.setWorkfaceId(workface.getWorkfaceId());
                reportVo.setReportStatus("0");
                reportVo.setReportStatusName("未上报");
            }
            
            // 设置工作面和项目信息
            reportVo.setWorkfaceName(workface.getWorkfaceName());
            reportVo.setProjectId(workface.getProjectId());
            reportVo.setProjectName(workface.getProjectName());
            
            resultList.add(reportVo);
        }
        
        return resultList;
    }

    /**
     * 实体转换为VO
     */
    private DgswrReportVo convertToVo(DgswrReport report) {
        DgswrReportVo vo = new DgswrReportVo();
        BeanUtils.copyProperties(report, vo);
        
        // 设置状态名称
        if ("1".equals(report.getReportStatus())) {
            vo.setReportStatusName("已上报");
        } else {
            vo.setReportStatusName("草稿");
        }
        
        // 查询工作面信息
        if (StringUtils.hasText(report.getWorkfaceId())) {
            DgswrWorkfaceVo workface = dgswrWorkfaceService.queryById(report.getWorkfaceId());
            if (workface != null) {
                vo.setWorkfaceName(workface.getWorkfaceName());
                vo.setProjectId(workface.getProjectId());
                vo.setProjectName(workface.getProjectName());
            }
        }
        
        return vo;
    }
}
