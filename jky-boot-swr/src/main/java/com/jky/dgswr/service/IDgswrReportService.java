package com.jky.dgswr.service;

import com.jky.dgswr.domain.bo.DgswrReportBo;
import com.jky.dgswr.domain.vo.DgswrReportVo;
import com.jky.dgswr.domain.vo.DgswrWorkfaceReportVo;

import java.util.List;

/**
 * 上报内容管理Service接口
 */
public interface IDgswrReportService {

    /**
     * 查询上报列表
     *
     * @param query 查询条件
     * @return 上报列表
     */
    List<DgswrReportVo> selectReportList(DgswrReportBo query);

    /**
     * 根据上报ID查询上报信息
     *
     * @param reportId 上报ID
     * @return 上报信息
     */
    DgswrReportVo queryById(String reportId);

    /**
     * 新增上报
     *
     * @param bo 上报信息
     * @return 是否成功
     */
    Boolean insert(DgswrReportBo bo);

    /**
     * 修改上报
     *
     * @param bo 上报信息
     * @return 是否成功
     */
    Boolean update(DgswrReportBo bo);

    /**
     * 删除上报
     *
     * @param reportId 上报ID
     * @return 是否成功
     */
    Boolean deleteById(String reportId);

    /**
     * 查询工作面上报列表（包含未上报的工作面）
     *
     * @param query 查询条件
     * @return 工作面上报列表
     */
    List<DgswrWorkfaceReportVo> selectWorkfaceReportList(DgswrReportBo query);
}
