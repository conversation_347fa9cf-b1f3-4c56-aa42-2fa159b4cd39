package com.jky.dgswr.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jky.dgswr.domain.DgswrProject;
import com.jky.dgswr.domain.bo.DgswrProjectBo;
import com.jky.dgswr.domain.vo.DgswrProjectVo;
import com.jky.dgswr.domain.vo.DgswrWorkfaceVo;
import com.jky.dgswr.mapper.DgswrProjectMapper;
import com.jky.dgswr.service.IDgswrProjectService;
import com.jky.dgswr.service.IDgswrWorkfaceService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: lpg
 * @Date: 2025/06/16
 * @Description:
 */
@Service
@RequiredArgsConstructor
public class DgswrProjectServiceImpl extends ServiceImpl<DgswrProjectMapper, DgswrProject> implements IDgswrProjectService {

    private final DgswrProjectMapper dgswrProjectMapper;
    private final IDgswrWorkfaceService dgswrWorkfaceService;

    @Override
    public List<DgswrProjectVo> selectProjectList(DgswrProjectBo query) {
        LambdaQueryWrapper<DgswrProject> lqw = Wrappers.<DgswrProject>lambdaQuery();
        lqw.like(StringUtils.isNotBlank(query.getProjectName()), DgswrProject::getProjectName, query.getProjectName());
        lqw.eq(StringUtils.isNotBlank(query.getStatus()), DgswrProject::getStatus, query.getStatus());
        lqw.orderByDesc(DgswrProject::getCreateTime);
        List<DgswrProject> dgswrProjects = dgswrProjectMapper.selectList(lqw);
        List<DgswrProjectVo> dgswrProjectVos = BeanUtil.copyToList(dgswrProjects, DgswrProjectVo.class);

        // 查询项目下的工作面
        for (DgswrProjectVo projectVo : dgswrProjectVos) {
            List<DgswrWorkfaceVo> workfaceList = dgswrWorkfaceService.selectWorkfaceListByProjectId(projectVo.getProjectId());
            projectVo.setWorkfaceList(workfaceList);
        }

        return dgswrProjectVos;
    }

    @Override
    public DgswrProjectVo queryById(String projectId) {
        DgswrProject dgswrProject = dgswrProjectMapper.selectById(projectId);
        return BeanUtil.copyProperties(dgswrProject, DgswrProjectVo.class);
    }

    @Override
    public Boolean insert(DgswrProjectBo projectBo) {
        DgswrProject dgswrProject = BeanUtil.copyProperties(projectBo, DgswrProject.class);
        return dgswrProjectMapper.insert(dgswrProject) > 0;
    }

    @Override
    public Boolean update(DgswrProjectBo projectBo) {
        DgswrProject dgswrProject = BeanUtil.copyProperties(projectBo, DgswrProject.class);
        return dgswrProjectMapper.updateById(dgswrProject) > 0;
    }

    @Override
    public Boolean deleteById(String projectId) {
        return dgswrProjectMapper.deleteById(projectId) > 0;
    }
}
