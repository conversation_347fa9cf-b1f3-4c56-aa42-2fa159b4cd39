package com.jky.dgswr.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 上报内容表VO
 */
@Data
public class DgswrReportVo implements Serializable {

    /**
     * 上报ID
     */
    private String reportId;

    /**
     * 工作面ID
     */
    private String workfaceId;

    /**
     * 工作面名称
     */
    private String workfaceName;

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 上报内容
     */
    private String reportContent;

    /**
     * 上报图片（多个图片用逗号分隔）
     */
    private String reportImages;

    /**
     * 上报时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date reportTime;

    /**
     * 上报用户ID
     */
    private Long reportUserId;

    /**
     * 上报人姓名
     */
    private String reportUserName;

    /**
     * 上报单位
     */
    private String reportUnit;

    /**
     * 上报状态（0-草稿，1-已上报）
     */
    private String reportStatus;

    /**
     * 上报状态名称
     */
    private String reportStatusName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}
