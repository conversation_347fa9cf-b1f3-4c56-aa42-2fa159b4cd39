package com.jky.dgswr.domain.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * 上报表
 */
@Data
public class DgswrReportBo implements Serializable {

    /**
     * 上报ID
     */
    private String reportId;

    /**
     * 工作面ID
     */
    @NotBlank(message = "工作面ID不能为空")
    private String workfaceId;

    /**
     * 工作面名称
     */
    private String workfaceName;

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 上报内容
     */
    @NotBlank(message = "上报内容不能为空")
    private String reportContent;

    /**
     * 上报文件（图片等文件路径，多个用逗号分隔）
     */
    private String reportFile;

    /**
     * 上报时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date reportTime;

    /**
     * 上报用户ID
     */
    private String reportUserId;

    /**
     * 上报用户名称
     */
    private String reportUserName;

    /**
     * 上报单位
     */
    private String reportUnit;

    /**
     * 上报状态（0-未上报 1-已上报）
     */
    private String reportStatus;

    /**
     * 备注
     */
    private String remark;

}
