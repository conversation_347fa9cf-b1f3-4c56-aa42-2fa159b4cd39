package com.jky.dgswr.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 工作面表
 */
@Data
@TableName("dgswr_workfaces")
public class DgswrWorkface implements Serializable {

    /**
     * 工作面ID
     */
    private String workfaceId;

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 工作面名称
     */
    private String workfaceName;

    /**
     * 地址
     */
    private String address;

    /**
     * 纬度
     */
    private String lat;

    /**
     * 经度
     */
    private String lon;

    /**
     * 备注
     */
    private String remark;

    /**
     * 工作面介绍
     */
    private String introduction;

    /**
     * 工作面详情
     */
    private String details;


    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updateBy;
}
