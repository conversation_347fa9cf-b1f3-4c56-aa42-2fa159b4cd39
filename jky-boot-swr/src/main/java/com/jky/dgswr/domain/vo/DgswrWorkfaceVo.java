package com.jky.dgswr.domain.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 工作面表
 */
@Data
public class DgswrWorkfaceVo implements Serializable {

    /**
     * 工作面ID
     */
    private String workfaceId;

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 工作面名称
     */
    private String workfaceName;

    /**
     * 地址
     */
    private String address;

    /**
     * 纬度
     */
    private String lat;

    /**
     * 经度
     */
    private String lon;

    /**
     * 备注
     */
    private String remark;

}
