package com.jky.dgswr.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 工作面上报列表VO
 */
@Data
public class DgswrWorkfaceReportVo implements Serializable {

    /**
     * 工作面ID
     */
    private String workfaceId;

    /**
     * 工作面名称
     */
    private String workfaceName;

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 工作面地址
     */
    private String address;

    /**
     * 工作面介绍
     */
    private String introduction;

    /**
     * 工作面详情
     */
    private String details;

    /**
     * 最新上报内容
     */
    private String reportContent;

    /**
     * 最新上报图片（多个图片用逗号分隔）
     */
    private String reportImages;

    /**
     * 上报状态（0-未上报，1-已上报）
     */
    private String reportStatus;

    /**
     * 上报状态名称
     */
    private String reportStatusName;

    /**
     * 上报人姓名
     */
    private String reportUserName;

    /**
     * 上报单位
     */
    private String reportUnit;

    /**
     * 最新上报时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date latestReportTime;

    /**
     * 最新上报ID（用于操作）
     */
    private String latestReportId;

    /**
     * 备注
     */
    private String remark;
}
