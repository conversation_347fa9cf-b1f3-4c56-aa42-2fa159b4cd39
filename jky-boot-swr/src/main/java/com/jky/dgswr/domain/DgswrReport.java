package com.jky.dgswr.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 上报内容表
 */
@Data
@TableName("dgswr_reports")
public class DgswrReport implements Serializable {

    /**
     * 上报ID
     */
    @TableId
    private String reportId;

    /**
     * 工作面ID
     */
    private String workfaceId;

    /**
     * 上报内容
     */
    private String reportContent;

    /**
     * 上报图片（多个图片用逗号分隔）
     */
    private String reportImages;

    /**
     * 上报时间
     */
    private Date reportTime;

    /**
     * 上报用户ID
     */
    private Long reportUserId;

    /**
     * 上报人姓名
     */
    private String reportUserName;

    /**
     * 上报单位
     */
    private String reportUnit;

    /**
     * 上报状态（0-草稿，1-已上报）
     */
    private String reportStatus;

    /**
     * 备注
     */
    private String remark;


    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updateBy;

}
