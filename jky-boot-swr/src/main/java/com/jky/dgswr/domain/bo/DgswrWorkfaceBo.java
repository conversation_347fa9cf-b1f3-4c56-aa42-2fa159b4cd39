package com.jky.dgswr.domain.bo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 工作面表
 */
@Data
public class DgswrWorkfaceBo implements Serializable {

    /**
     * 工作面ID
     */
    private String workfaceId;

    /**
     * 项目ID
     */
    @NotBlank(message = "项目ID不能为空")
    private String projectId;

    /**
     * 工作面名称
     */
    @NotBlank(message = "工作面名称不能为空")
    private String workfaceName;

    /**
     * 地址
     */
    private String address;

    /**
     * 纬度
     */
    private String lat;

    /**
     * 经度
     */
    private String lon;

    /**
     * 备注
     */
    private String remark;

}
