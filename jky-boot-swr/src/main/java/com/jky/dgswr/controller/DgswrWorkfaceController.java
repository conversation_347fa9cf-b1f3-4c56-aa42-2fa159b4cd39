package com.jky.dgswr.controller;

import com.jky.dgswr.domain.bo.DgswrWorkfaceBo;
import com.jky.dgswr.domain.vo.DgswrWorkfaceVo;
import com.jky.dgswr.service.IDgswrWorkfaceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.jeecg.common.api.vo.Result;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 工作面管理
 */
@Validated
@Api(value = "工作面管理", tags = {"工作面管理"})
@RestController
@RequestMapping("/swr/workface")
@RequiredArgsConstructor
public class DgswrWorkfaceController {

    private final IDgswrWorkfaceService dgswrWorkfaceService;

    /**
     * 查询工作面列表
     */
    @ApiOperation("查询工作面列表(dgswr-workface-list)")
//    @RequiresPermissions("dgswr-workface-list")
    @GetMapping("/list")
    public Result<List<DgswrWorkfaceVo>> list(DgswrWorkfaceBo query) {
        List<DgswrWorkfaceVo> list = dgswrWorkfaceService.selectWorkfaceList(query);
        return Result.OK("查询成功", list);
    }

    /**
     * 根据项目ID查询工作面列表
     */
    @ApiOperation("根据项目ID查询工作面列表(dgswr-workface-list-by-project)")
//    @RequiresPermissions("dgswr-workface-list")
    @GetMapping("/project/{projectId}")
    public Result<List<DgswrWorkfaceVo>> listByProjectId(@PathVariable("projectId") String projectId) {
        List<DgswrWorkfaceVo> list = dgswrWorkfaceService.selectWorkfaceListByProjectId(projectId);
        return Result.OK("查询成功", list);
    }

    /**
     * 查询工作面信息
     */
    @ApiOperation("查询工作面信息(dgswr-workface-query)")
//    @RequiresPermissions("dgswr-workface-query")
    @GetMapping("/{workfaceId}")
    public Result<DgswrWorkfaceVo> queryById(@PathVariable("workfaceId") String workfaceId) {
        return Result.OK("查询成功", dgswrWorkfaceService.queryById(workfaceId));
    }

    /**
     * 新增工作面
     */
    @ApiOperation("新增工作面(dgswr-workface-add)")
//    @RequiresPermissions("dgswr-workface-add")
    @PostMapping
    public Result<Boolean> add(@Validated @RequestBody DgswrWorkfaceBo bo) {
        return Result.OK("操作成功", dgswrWorkfaceService.insert(bo));
    }

    /**
     * 修改工作面
     */
    @ApiOperation("修改工作面(dgswr-workface-edit)")
//    @RequiresPermissions("dgswr-workface-edit")
    @PutMapping
    public Result<Boolean> edit(@Validated @RequestBody DgswrWorkfaceBo bo) {
        return Result.OK("操作成功", dgswrWorkfaceService.update(bo));
    }

    /**
     * 删除工作面
     */
    @ApiOperation("删除工作面(dgswr-workface-remove)")
//    @RequiresPermissions("dgswr-workface-remove")
    @DeleteMapping("/{workfaceId}")
    public Result<Boolean> delete(@ApiParam("工作面ID") @NotEmpty(message = "工作面ID不能为空") @PathVariable("workfaceId") String workfaceId) {
        return Result.OK("操作成功", dgswrWorkfaceService.deleteById(workfaceId));
    }

}
