package com.jky.dgswr.controller;

import com.jky.dgswr.domain.bo.DgswrReportBo;
import com.jky.dgswr.domain.vo.DgswrReportVo;
import com.jky.dgswr.service.IDgswrReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.jeecg.common.api.vo.Result;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 工作面信息上报管理
 */
@Validated
@Api(value = "工作面信息上报管理", tags = {"工作面信息上报管理"})
@RestController
@RequestMapping("/swr/report")
@RequiredArgsConstructor
public class DgswrReportController {

    private final IDgswrReportService dgswrReportService;

    /**
     * 查询工作面上报列表
     * 支持按项目名称、工作面名称、上报日期搜索
     * 展示工作面名称、上报内容、上报图片、上报状态、上报人、上报单位、最新上报时间
     */
    @ApiOperation("查询工作面上报列表(dgswr-report-workface-list) - 支持按项目名称、工作面名称、上报日期搜索")
//    @RequiresPermissions("dgswr-report-list")
    @GetMapping("/workface/list")
    public Result<List<DgswrReportVo>> getWorkfaceReportList(DgswrReportBo query) {
        List<DgswrReportVo> list = dgswrReportService.selectWorkfaceReportList(query);
        return Result.OK("查询成功", list);
    }

    /**
     * 查询上报列表
     */
    @ApiOperation("查询上报列表(dgswr-report-list)")
//    @RequiresPermissions("dgswr-report-list")
    @GetMapping("/list")
    public Result<List<DgswrReportVo>> list(DgswrReportBo query) {
        List<DgswrReportVo> list = dgswrReportService.selectReportList(query);
        return Result.OK("查询成功", list);
    }

    /**
     * 查询上报详情
     */
    @ApiOperation("查询上报详情(dgswr-report-detail)")
//    @RequiresPermissions("dgswr-report-query")
    @GetMapping("/{reportId}")
    public Result<DgswrReportVo> getReportDetail(@PathVariable("reportId") String reportId) {
        DgswrReportVo report = dgswrReportService.queryById(reportId);
        if (report == null) {
            Result<DgswrReportVo> result = new Result<>();
            result.error500("上报记录不存在");
            return result;
        }
        return Result.OK("查询成功", report);
    }

    /**
     * 新增上报
     */
    @ApiOperation("新增上报(dgswr-report-add)")
//    @RequiresPermissions("dgswr-report-add")
    @PostMapping
    public Result<Boolean> addReport(@Validated @RequestBody DgswrReportBo bo) {
        return Result.OK("操作成功", dgswrReportService.insert(bo));
    }

    /**
     * 修改上报
     */
    @ApiOperation("修改上报(dgswr-report-edit)")
//    @RequiresPermissions("dgswr-report-edit")
    @PutMapping("/{reportId}")
    public Result<Boolean> editReport(@PathVariable("reportId") String reportId,
                                      @Validated @RequestBody DgswrReportBo bo) {
        // 验证上报记录是否存在
        DgswrReportVo existingReport = dgswrReportService.queryById(reportId);
        if (existingReport == null) {
            Result<Boolean> result = new Result<>();
            result.error500("上报记录不存在");
            return result;
        }

        // 确保上报ID正确
        bo.setReportId(reportId);
        return Result.OK("操作成功", dgswrReportService.update(bo));
    }

    /**
     * 删除上报
     */
    @ApiOperation("删除上报(dgswr-report-remove)")
//    @RequiresPermissions("dgswr-report-remove")
    @DeleteMapping("/{reportId}")
    public Result<Boolean> deleteReport(@ApiParam("上报ID") @NotEmpty(message = "上报ID不能为空") @PathVariable("reportId") String reportId) {
        // 验证上报记录是否存在
        DgswrReportVo existingReport = dgswrReportService.queryById(reportId);
        if (existingReport == null) {
            Result<Boolean> result = new Result<>();
            result.error500("上报记录不存在");
            return result;
        }

        return Result.OK("操作成功", dgswrReportService.deleteById(reportId));
    }

    /**
     * 工作面上报功能
     */
    @ApiOperation("工作面上报功能(dgswr-workface-report)")
//    @RequiresPermissions("dgswr-workface-report")
    @PostMapping("/workface/{workfaceId}")
    public Result<Boolean> reportWorkface(@PathVariable("workfaceId") String workfaceId,
                                          @Validated @RequestBody DgswrReportBo bo) {
        // 确保工作面ID正确
        bo.setWorkfaceId(workfaceId);
        return Result.OK("上报成功", dgswrReportService.insert(bo));
    }
}
