package com.jky.dgswr.controller;

import com.jky.dgswr.domain.bo.DgswrProjectBo;
import com.jky.dgswr.domain.bo.DgswrWorkfaceBo;
import com.jky.dgswr.domain.vo.DgswrProjectVo;
import com.jky.dgswr.domain.vo.DgswrWorkfaceVo;
import com.jky.dgswr.service.IDgswrProjectService;
import com.jky.dgswr.service.IDgswrWorkfaceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.jeecg.common.api.vo.Result;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 项目管理
 */
@Validated
@Api(value = "项目管理", tags = {"项目管理"})
@RestController
@RequestMapping("/swr/project")
@RequiredArgsConstructor
public class DgswrProjectController {

    private final IDgswrProjectService dgswrProjectService;
    private final IDgswrWorkfaceService dgswrWorkfaceService;

    /**
     * 查询项目列表
     */
    @ApiOperation("查询项目列表(dgswr-project-list)")
//    @RequiresPermissions("dgswr-project-list")
    @GetMapping("/list")
    public Result<List<DgswrProjectVo>> list(DgswrProjectBo query) {
        List<DgswrProjectVo> list = dgswrProjectService.selectProjectList(query);
        return Result.OK("查询成功", list);
    }

    /**
     * 查询项目信息
     */
    @ApiOperation("查询项目信息(dgdoc-project-query)")
//    @RequiresPermissions("dgswr-project-query")
    @GetMapping("/{projectId}")
    public Result<DgswrProjectVo> queryById(@PathVariable("projectId") String projectId) {
        return Result.OK("查询成功", dgswrProjectService.queryById(projectId));
    }

    /**
     * 新增项目
     */
    @ApiOperation("新增项目(dgswr-project-add)")
//    @RequiresPermissions("dgswr-project-add")
    @PostMapping
    public Result<Boolean> add(@Validated @RequestBody DgswrProjectBo bo) {
        return Result.OK("操作成功", dgswrProjectService.insert(bo));
    }

    /**
     * 修改项目
     */
    @ApiOperation("修改项目(dgswr-project-edit)")
//    @RequiresPermissions("dgswr-project-edit")
    @PutMapping
    public Result<Boolean> edit(@Validated @RequestBody DgswrProjectBo bo) {
        return Result.OK("操作成功", dgswrProjectService.update(bo));
    }

    /**
     * 删除项目
     */
    @ApiOperation("删除项目(dgswr-project-remove)")
//    @RequiresPermissions("dgswr-project-remove")
    @DeleteMapping("/{projectId}")
    public Result<Boolean> delete(@ApiParam("项目ID") @NotEmpty(message = "项目ID不能为空") @PathVariable("projectId") String projectId) {
        return Result.OK("操作成功", dgswrProjectService.deleteById(projectId));
    }

    // ==================== 工作面管理接口 ====================



    /**
     * 查询工作面详情
     */
    @ApiOperation("查询工作面详情(dgswr-workface-detail)")
//    @RequiresPermissions("dgswr-workface-query")
    @GetMapping("/workfaces/{workfaceId}")
    public Result<DgswrWorkfaceVo> getWorkfaceDetail(@PathVariable("workfaceId") String workfaceId) {
        DgswrWorkfaceVo workface = dgswrWorkfaceService.queryById(workfaceId);
        if (workface == null) {
            Result<DgswrWorkfaceVo> result = new Result<>();
            result.error500("工作面不存在");
            return result;
        }
        return Result.OK("查询成功", workface);
    }

    /**
     * 新增工作面
     */
    @ApiOperation("新增工作面(dgswr-workface-add)")
//    @RequiresPermissions("dgswr-workface-add")
    @PostMapping("/workfaces")
    public Result<Boolean> addWorkface(@Validated @RequestBody DgswrWorkfaceBo bo) {
        return Result.OK("操作成功", dgswrWorkfaceService.insert(bo));
    }

    /**
     * 修改工作面
     */
    @ApiOperation("修改工作面(dgswr-workface-edit)")
//    @RequiresPermissions("dgswr-workface-edit")
    @PutMapping("/workfaces/{workfaceId}")
    public Result<Boolean> editWorkface(@PathVariable("workfaceId") String workfaceId,
                                        @Validated @RequestBody DgswrWorkfaceBo bo) {
        // 验证工作面是否存在
        DgswrWorkfaceVo existingWorkface = dgswrWorkfaceService.queryById(workfaceId);
        if (existingWorkface == null) {
            Result<Boolean> result = new Result<>();
            result.error500("工作面不存在");
            return result;
        }

        // 确保工作面ID正确
        bo.setWorkfaceId(workfaceId);
        return Result.OK("操作成功", dgswrWorkfaceService.update(bo));
    }

    /**
     * 删除工作面
     */
    @ApiOperation("删除工作面(dgswr-workface-remove)")
//    @RequiresPermissions("dgswr-workface-remove")
    @DeleteMapping("/workfaces/{workfaceId}")
    public Result<Boolean> deleteWorkface(@PathVariable("workfaceId") String workfaceId) {
        // 验证工作面是否存在
        DgswrWorkfaceVo existingWorkface = dgswrWorkfaceService.queryById(workfaceId);
        if (existingWorkface == null) {
            Result<Boolean> result = new Result<>();
            result.error500("工作面不存在");
            return result;
        }

        return Result.OK("操作成功", dgswrWorkfaceService.deleteById(workfaceId));
    }

}
