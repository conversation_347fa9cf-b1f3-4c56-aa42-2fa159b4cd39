package com.jky.modules.erp.goods.service.impl;

import com.jky.modules.erp.goods.entity.ErpGoodsPropertyItem;
import com.jky.modules.erp.goods.mapper.ErpGoodsPropertyItemMapper;
import com.jky.modules.erp.goods.service.IErpGoodsPropertyItemService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: erp_goods_property_item
 * @Author: jky
 * @Date:   2022-08-29
 * @Version: V1.0
 */
@Service
public class ErpGoodsPropertyItemServiceImpl extends ServiceImpl<ErpGoodsPropertyItemMapper, ErpGoodsPropertyItem> implements IErpGoodsPropertyItemService {

}
