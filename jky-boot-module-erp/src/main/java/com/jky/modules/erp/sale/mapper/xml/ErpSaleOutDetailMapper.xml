<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jky.modules.erp.sale.mapper.ErpSaleOutDetailMapper">

	<delete id="deleteByMainId" parameterType="java.lang.String">
		DELETE 
		FROM  erp_sale_out_detail 
		WHERE
			 sheet_id = #{mainId} 	</delete>
	
	<select id="selectByMainId" parameterType="java.lang.String" resultType="com.jky.modules.erp.sale.entity.ErpSaleOutDetail">
		SELECT * 
		FROM  erp_sale_out_detail
		WHERE
			 sheet_id = #{mainId} 	</select>
</mapper>
