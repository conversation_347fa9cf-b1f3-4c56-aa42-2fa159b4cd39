<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jky.modules.erp.goods.mapper.ErpGoodsPriceMapper">

	<delete id="deleteByMainId" parameterType="java.lang.String">
		DELETE 
		FROM  erp_goods_price 
		WHERE
			 id = #{mainId} 	</delete>
	
	<select id="selectByMainId" parameterType="java.lang.String" resultType="com.jky.modules.erp.goods.entity.ErpGoodsPrice">
		SELECT * 
		FROM  erp_goods_price
		WHERE
			 id = #{mainId} 	</select>
</mapper>
