<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jky.modules.erp.sale.mapper.ErpSaleOutDtoMapper">

  <resultMap id="ErpSaleOutDtoMap" type="com.jky.modules.erp.sale.dto.ErpSaleOutDto">
        <id column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="sc_id" property="scId"/>
        <result column="customer_id" property="customerId"/>
        <result column="saler_id" property="salerId"/>
        <result column="payment_date" property="paymentDate"/>
        <result column="sale_order_id" property="saleOrderId"/>
        <result column="total_num" property="totalNum"/>
        <result column="total_gift_num" property="totalGiftNum"/>
        <result column="total_amount" property="totalAmount"/>
        <result column="description" property="description"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="approve_by" property="approveBy"/>
        <result column="approve_time" property="approveTime"/>
        <result column="status" property="status"/>
        <result column="refuse_reason" property="refuseReason"/>
        <result column="settle_status" property="settleStatus"/>
        <result column="ws_code" property="wsCode"/>
        <result column="ws_name" property="wsName"/>
        <result column="cs_code" property="csCode"/>
        <result column="cs_name" property="csName"/>
        <result column="saler_name" property="salerName"/>
        
    </resultMap>
    <sql id="ErpSaleOutDto_sql">
        SELECT
            a.id,
            a.code,
            a.sc_id,
			a.customer_id,
			a.saler_id,
			a.payment_date,
			a.sale_order_id,
			a.total_num,
			a.total_gift_num,
			a.total_amount,
			a.description,
			a.create_by,
			a.create_time,
			a.approve_by,
			a.approve_time,
			a.status,
			a.refuse_reason,
			a.settle_status,
			ws.code as ws_code,
            ws.name as ws_name,
			cs.code as cs_code,
			cs.name as cs_name,
			u.realname saler_name
        FROM erp_sale_out AS a
        LEFT JOIN erp_warehouse AS ws ON ws.id = a.sc_id
        LEFT JOIN erp_customer AS cs ON cs.id = a.customer_id
        LEFT JOIN sys_user AS u ON u.username = a.saler_id
    </sql>
    <select id="querySaleOutList" resultMap="ErpSaleOutDtoMap">
        <include refid="ErpSaleOutDto_sql"/>
        <where>
           <if test="vo != null">
	            <if test="vo.scId != null and vo.scId != ''">
	                AND a.sc_id = #{vo.scId}
	            </if>
	            <if test="vo.customerId != null and vo.customerId != ''">
	                AND a.customer_id = #{vo.customerId} 
	            </if>
            </if>
        </where>
        ORDER BY a.code
    </select>
    
</mapper>