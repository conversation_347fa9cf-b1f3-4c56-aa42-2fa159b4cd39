<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jky.dgdoc.mapper.DgzjjzjJcjgCoreSampleMapper">


    <select id="queryMonomerJcjgList" resultType="com.jky.dgdoc.domain.vo.DgzjjzjJcjgCoreSampleTableVo">
        select dm.monomer_id,
               djcs.zjj_subject_id,
               djcs.zjj_section_id,
               djcs.zjj_section_code,
               djcs.synum,
               djcs.ypname,
               djcs.zjj_builder_license,
               djcs.report_file_url,
               djcs.reportdate,
               djcs.sup_deal_result,
               djcs.sup_deal_time
        from dgdoc_monomer dm
                 inner join dgzjjzj_jcjg_core_sample djcs on
            dm.project_id = djcs.zjj_subject_id and dm.licencemoney = djcs.zjj_builder_license and noteip in ('n', 'N')
        where dm.monomer_id = #{monomerId}
        order by djcs.reportdate desc
    </select>
    <select id="queryMonomerJcjgByJcbh" resultType="com.jky.dgdoc.domain.vo.DgzjjzjSupJcjgUnqualifiedReplyFileVo">
        select id,
               sy_num,
               module_file_name,
               file_name,
               file_url,
               create_time
        from dgzjjzj_sup_jcjg_unqualified_reply_file
        where sy_num = #{synum}
          and invalid = 0
        order by create_time desc
    </select>
    <select id="queryMonomerJcjgUnqualified" resultType="java.lang.String">
        SELECT EXISTS (SELECT 1
                       FROM dgdoc_monomer dm
                                INNER JOIN dgzjjzj_jcjg_core_sample djcs ON
                           dm.project_id = djcs.zjj_subject_id AND
                           dm.licencemoney = djcs.zjj_builder_license AND
                           djcs.noteip IN ('n', 'N')
                       WHERE dm.monomer_id = #{monomerId}) AS monomerReportStatus;
    </select>
</mapper>