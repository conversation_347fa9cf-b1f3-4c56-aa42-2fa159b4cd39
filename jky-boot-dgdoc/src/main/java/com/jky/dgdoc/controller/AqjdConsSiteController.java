package com.jky.dgdoc.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jky.dgdoc.domain.*;
import com.jky.dgdoc.domain.bo.MonomerBo;
import com.jky.dgdoc.domain.query.MonomerEntQuery;
import com.jky.dgdoc.domain.query.MonomerQuery;
import com.jky.dgdoc.domain.query.PdProjectCollectQuery;
import com.jky.dgdoc.domain.query.ProjectQuery;
import com.jky.dgdoc.domain.vo.*;
import com.jky.dgdoc.service.IDgdocAqjdConsSiteService;
import com.jky.dgdoc.service.IDgdocMonomerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.Arrays;
import java.util.List;

/**
 * 工地管理
 */
@Validated
@Api(value = "工地管理", tags = {"工地管理"})
@RestController
@RequestMapping("/aqidConsSite")
@RequiredArgsConstructor
public class AqjdConsSiteController {
    private final IDgdocAqjdConsSiteService dgdocAqjdConsSiteService;




    /**
     * 查询工地列表
     */
    @ApiOperation("查询工地列表(dgdoc-project-list)")
    @RequiresPermissions("dgdoc-project-list")
    @GetMapping("/list")
    public Result<IPage<DgdocAqjdConsSiteVo>> list(DgdocAqjdConsSiteVo query, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                 @RequestParam(name = "pageSize", defaultValue = "300") Integer pageSize) {
        IPage<DgdocAqjdConsSiteVo> page = dgdocAqjdConsSiteService.queryList(query, pageNo, pageSize);
        Result<IPage<DgdocAqjdConsSiteVo>> result = new Result<>();
        result.setSuccess(true);
        result.setResult(page);
        return result;
    }

    /**
     * 查询工地信息
     */
    @ApiOperation("查询工地信息(dgdoc-project-query)")
    @RequiresPermissions("dgdoc-project-query")
    @GetMapping("/{consSiteId}")
    public Result<DgdocAqjdConsSite> queryById(@PathVariable("consSiteId") String consSiteId) {
        DgdocAqjdConsSite pdProjectCollect = dgdocAqjdConsSiteService.getById(consSiteId);
        return Result.OK(pdProjectCollect);
    }

}
