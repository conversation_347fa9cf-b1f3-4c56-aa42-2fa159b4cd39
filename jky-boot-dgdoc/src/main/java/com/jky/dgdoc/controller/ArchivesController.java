package com.jky.dgdoc.controller;

import com.jky.dgdoc.domain.bo.CjEntConfirmBo;
import com.jky.dgdoc.domain.query.ArchivesProcessQuery;
import com.jky.dgdoc.domain.query.ArchivesQuery;
import com.jky.dgdoc.domain.vo.DgdocArchivesInstanceVo;
import com.jky.dgdoc.service.IDgdocArchivesInstanceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.Arrays;
import java.util.List;

/**
 * 档案管理
 */
@Validated
@Api(value = "档案管理", tags = {"档案管理"})
@RestController
@RequestMapping("/archives")
@RequiredArgsConstructor
public class ArchivesController {

    private final IDgdocArchivesInstanceService gddocArchivesInstanceService;

    /**
     * 查询档案列表
     */
    @ApiOperation("查询档案列表(dgdoc-archives-list)")
    @RequiresPermissions("dgdoc-archives-list")
    @GetMapping("/list")
    public Result<List<DgdocArchivesInstanceVo>> list(ArchivesQuery query) {
        Result<List<DgdocArchivesInstanceVo>> result = new Result<>();

        List<DgdocArchivesInstanceVo> archives = gddocArchivesInstanceService.treeList(query);
        result.setResult(archives);
        return result;
    }

    /**
     * 查询项目验收进度
     */
    @ApiOperation("查询项目验收进度(dgdoc-archives-list)")
    @RequiresPermissions("dgdoc-archives-list")
    @GetMapping("/processList")
    public Result<Object> processList(ArchivesProcessQuery query) {
        Result<Object> result = new Result<>();

        Object archives = gddocArchivesInstanceService.processList(query);
        result.setResult(archives);
        return result;
    }

    /**
     * 审核
     */
    @ApiOperation("审核(dgdoc-archives-audit)")
    @RequiresPermissions("dgdoc-archives-audit")
    @GetMapping("/audit/{archivesInstanceIds}")
    public Result<Boolean> audit(@NotEmpty(message = "档案ID不能为空") @PathVariable String[] archivesInstanceIds) {
        Result<Boolean> result = new Result<>();
        Boolean flag = gddocArchivesInstanceService.audit(Arrays.asList(archivesInstanceIds));
        if (flag) {
            result.success("审核成功！");
        } else {
            result.error500("审核失败！");
        }
        return result;
    }

    /**
     * 退回
     */
    @ApiOperation("退回(dgdoc-archives-return)")
    @RequiresPermissions("dgdoc-archives-return")
    @GetMapping("/return/{archivesInstanceId}")
    public Result<Boolean> returnBack(@PathVariable("archivesInstanceId") String archivesInstanceId) {
        Result<Boolean> result = new Result<>();
        Boolean flag = gddocArchivesInstanceService.returnBack(archivesInstanceId);
        if (flag) {
            result.success("退回成功！");
        } else {
            result.error500("退回失败！");
        }
        return result;
    }

    /**
     * 上传档案
     */
    @ApiOperation("上传档案(dgdoc-archives-upload)")
    @RequiresPermissions("dgdoc-archives-upload")
    @PostMapping("/upload/{archivesInstanceId}")
    public Result<Boolean> upload(@NotBlank(message = "档案ID不能为空") @PathVariable String archivesInstanceId,
                                  HttpServletRequest request) {
        Result<Boolean> result = new Result<>();
        Boolean flag = gddocArchivesInstanceService.upload(archivesInstanceId, request);
        if (flag) {
            result.success("上传成功！");
        } else {
            result.error500("上传失败！");
        }
        return result;
    }

    /**
     * 作废或恢复
     */
    @ApiOperation("作废或恢复(dgdoc-archives-invalid)")
    @RequiresPermissions("dgdoc-archives-invalid")
    @GetMapping("/invalid/{archivesInstanceId}")
    public Result<Boolean> invalid(@NotBlank(message = "档案ID不能为空") @PathVariable String archivesInstanceId, String status) {
        Result<Boolean> result = new Result<>();
        Boolean flag = gddocArchivesInstanceService.updateStatus(archivesInstanceId, status);
        if (flag) {
            result.success("操作成功！");
        } else {
            result.error500("操作失败！");
        }
        return result;
    }

    /**
     * 预览图片或者PDF
     */
    @ApiOperation("预览图片或者PDF(dgdoc-archives-preview)")
    @RequiresPermissions("dgdoc-archives-preview")
    @GetMapping("/preview/{archivesInstanceId}")
    public void preview(@NotBlank(message = "档案ID不能为空") @PathVariable String archivesInstanceId, HttpServletResponse response) {
        gddocArchivesInstanceService.preview(archivesInstanceId, response);
    }

    /**
     * 删除档案文件
     */
    @ApiOperation("删除档案文件(dgdoc-archives-file-remove)")
    @RequiresPermissions("dgdoc-archives-file-remove")
    @DeleteMapping("/file/{archivesInstanceId}")
    public Result<Boolean> removeFile(@NotBlank(message = "档案ID不能为空") @PathVariable String archivesInstanceId) {
        Result<Boolean> result = new Result<>();
        Boolean flag = gddocArchivesInstanceService.removeFile(archivesInstanceId);
        if (flag) {
            result.success("删除成功！");
        } else {
            result.error500("删除失败！");
        }
        return result;
    }

    /**
     * 参见方确认后档案上传
     */
    @ApiOperation("参见方确认后档案上传")
    @PostMapping("/cjEntConfirm")
    public Result<Boolean> cjEntConfirm(@Validated CjEntConfirmBo bo) {
        Result<Boolean> result = new Result<>();
        Boolean flag = gddocArchivesInstanceService.cjEntConfirm(bo);
        if (flag) {
            result.success("上传成功！");
        } else {
            result.error500("上传失败！");
        }
        return result;
    }

}
