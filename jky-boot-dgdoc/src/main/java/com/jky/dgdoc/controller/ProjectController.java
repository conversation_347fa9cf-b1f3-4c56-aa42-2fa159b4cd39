package com.jky.dgdoc.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jky.dgdoc.domain.PdProjectCollect;
import com.jky.dgdoc.domain.PdProjectEntCollect;
import com.jky.dgdoc.domain.Rs;
import com.jky.dgdoc.domain.query.PdProjectCollectQuery;
import com.jky.dgdoc.domain.query.ProjectEntQuery;
import com.jky.dgdoc.domain.query.ProjectQuery;
import com.jky.dgdoc.domain.vo.PdProjectCollectTableVo;
import com.jky.dgdoc.service.IDgdocProjectEntService;
import com.jky.dgdoc.service.IDgdocProjectService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.Arrays;
import java.util.List;

/**
 * 项目管理
 */
@Validated
@Api(value = "项目管理", tags = {"项目管理"})
@RestController
@RequestMapping("/project")
@RequiredArgsConstructor
public class ProjectController {

    private final IDgdocProjectService dgDocProjectService;
    private final IDgdocProjectEntService iDgdocProjectEntService;

    /**
     * 根据项目名称查询项目信息
     */
    @ApiOperation("根据项目名称查询项目信息")
    @GetMapping("/queryProject")
    public Result<List<PdProjectCollect>> queryProject(@Validated ProjectQuery query) {
        Result<List<PdProjectCollect>> result = new Result<>();
        Result<Rs<PdProjectCollect>> rsResult = dgDocProjectService.queryProject(query);
        if (!rsResult.isSuccess()) {
            result.error500(rsResult.getMessage());
            return result;
        }
        result.setResult(rsResult.getResult().getList());
        return result;
    }

    /**
     * 查询项目列表
     */
    @ApiOperation("查询项目列表(dgdoc-project-list)")
    @RequiresPermissions("dgdoc-project-list")
    @GetMapping("/list")
    public Result<IPage<PdProjectCollectTableVo>> list(PdProjectCollectQuery query, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                       @RequestParam(name = "pageSize", defaultValue = "300") Integer pageSize) {
        IPage<PdProjectCollectTableVo> page = dgDocProjectService.queryList(query, pageNo, pageSize);
        Result<IPage<PdProjectCollectTableVo>> result = new Result<>();
        result.setSuccess(true);
        result.setResult(page);
        return result;
    }

    /**
     * 查询项目信息
     */
    @ApiOperation("查询项目信息(dgdoc-project-query)")
    @RequiresPermissions("dgdoc-project-query")
    @GetMapping("/{projectId}")
    public Result<PdProjectCollect> queryById(@PathVariable("projectId") String projectId) {
        PdProjectCollect pdProjectCollect = dgDocProjectService.getById(projectId);
        return Result.OK(pdProjectCollect);
    }

    /**
     * 新增项目
     */
    @ApiOperation("新增项目(dgdoc-project-add)")
    @RequiresPermissions("dgdoc-project-add")
    @PostMapping
    public Result<PdProjectCollect> add(@Validated @RequestBody PdProjectCollect pdProjectCollect) {
        Result<PdProjectCollect> result = new Result<>();
        if (dgDocProjectService.insert(pdProjectCollect)) {
            result.success("添加成功！");
        } else {
            result.error500("添加失败！");
        }
        return result;
    }

    /**
     * 删除项目
     */
    @ApiOperation("删除项目(dgdoc-project-remove)")
    @RequiresPermissions("dgdoc-project-remove")
    @DeleteMapping("/{projectIds}")
    public Result<Void> delete(@ApiParam("项目ID") @NotEmpty(message = "项目ID不能为空") @PathVariable("projectIds") String[] projectIds) {
        Result<Void> result = new Result<>();
        if (dgDocProjectService.removeProject(Arrays.asList(projectIds))) {
            result.success("删除成功！");
        } else {
            result.error500("删除失败！");
        }
        return result;
    }

    /**
     * 项目查询参建单位
     */
    @ApiOperation("项目查询参建单位")
    @GetMapping("/queryEntInfo")
    public Result<List<PdProjectEntCollect>> queryEntInfo(@Validated ProjectEntQuery query) {
        Result<List<PdProjectEntCollect>> result = new Result<>();
        Result<Rs<PdProjectEntCollect>> rsResult = dgDocProjectService.queryEntInfo(query);
        if (!rsResult.isSuccess()) {
            result.error500(rsResult.getMessage());
            return result;
        }
        result.setResult(rsResult.getResult().getList());
        return result;
    }

    /**
     * 新增项目参建单位
     */
    @ApiOperation("新增项目参建单位(dgdoc-project-ent-add)")
    @RequiresPermissions("dgdoc-project-ent-add")
    @PostMapping("/ent")
    public Result<PdProjectEntCollect> addEnt(@RequestBody PdProjectEntCollect pdProjectEntCollect) {
        Result<PdProjectEntCollect> result = new Result<>();
        if (dgDocProjectService.insertEnt(pdProjectEntCollect)) {
            result.success("添加成功");
        } else {
            result.error500("添加失败");
        }
        return result;
    }

    /**
     * 查询参建单位信息
     */
    @ApiOperation("查询参建单位信息(dgdoc-project-ent-list)")
    @RequiresPermissions("dgdoc-project-ent-list")
    @GetMapping("/ent/{projectId}")
    public Result<List<PdProjectEntCollect>> queryEntInfo(@ApiParam("项目ID") @NotBlank(message = "项目ID不能为空") @PathVariable("projectId") String projectId) {
        Result<List<PdProjectEntCollect>> result = new Result<>();
        LambdaQueryWrapper<PdProjectEntCollect> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(PdProjectEntCollect::getProjectId, projectId);
        List<PdProjectEntCollect> pdProjectEntCollects = iDgdocProjectEntService.list(lambdaQueryWrapper);

        result.setResult(pdProjectEntCollects);
        return result;
    }

    /**
     * 删除项目参建单位
     */
    @ApiOperation("删除项目参建单位(dgdoc-project-ent-remove)")
    @RequiresPermissions("dgdoc-project-ent-remove")
    @DeleteMapping("/ent/{projectEntIds}")
    public Result<Void> deleteEnt(@ApiParam("项目参建单位ID") @NotEmpty(message = "项目参建单位ID不能为空") @PathVariable("projectEntIds") String[] projectEntIds) {
        Result<Void> result = new Result<>();
        if (iDgdocProjectEntService.removeByIds(Arrays.asList(projectEntIds))) {
            result.success("删除成功！");
        } else {
            result.error500("删除失败！");
        }
        return result;
    }


}
