package com.jky.dgdoc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jky.dgdoc.domain.DgdocArchives;

import java.util.List;

/**
 * 档案接口
 */
public interface IDgdocArchivesService extends IService<DgdocArchives> {

    /**
     * 根据档案类型查询档案列表
     */
    List<DgdocArchives> selectArchivesListByType(String archivesType);

    /**
     * 根据档案类型和是否市政查询档案列表
     *
     * @param archivesType
     * @param isSz
     * @return
     */
    List<DgdocArchives> selectArchivesListByTypeAndIsSz(String archivesType, Boolean isSz);

}
