package com.jky.dgdoc.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jky.dgdoc.domain.DgdocAqjdConsSite;
import com.jky.dgdoc.domain.DgdocArchives;
import com.jky.dgdoc.domain.query.PdProjectCollectQuery;
import com.jky.dgdoc.domain.vo.DgdocAqjdConsSiteVo;
import com.jky.dgdoc.domain.vo.PdProjectCollectTableVo;

import java.util.List;

/**
 * 档案接口
 */
public interface IDgdocAqjdConsSiteService extends IService<DgdocAqjdConsSite> {

    /**
     * 查询工地列表
     *
     * @param query
     * @param pageNo
     * @param pageSize
     * @return
     */
    IPage<DgdocAqjdConsSiteVo> queryList(DgdocAqjdConsSiteVo query, Integer pageNo, Integer pageSize);

}
