package com.jky.dgdoc.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jky.dgdoc.domain.PdProjectCollect;
import com.jky.dgdoc.domain.PdProjectEntCollect;
import com.jky.dgdoc.domain.Rs;
import com.jky.dgdoc.domain.query.PdProjectCollectQuery;
import com.jky.dgdoc.domain.query.ProjectEntQuery;
import com.jky.dgdoc.domain.query.ProjectQuery;
import com.jky.dgdoc.domain.vo.PdProjectCollectTableVo;
import org.jeecg.common.api.vo.Result;

import java.util.List;

/**
 * @Author: lpg
 * @Date: 2024/01/02/16:48
 * @Description:
 */
public interface IDgdocProjectService extends IService<PdProjectCollect> {
    /*
     * 查询项目信息
     */
    Result<Rs<PdProjectCollect>> queryProject(ProjectQuery query);

    /**
     * 查询项目列表
     *
     * @param query
     * @param pageNo
     * @param pageSize
     * @return
     */
    IPage<PdProjectCollectTableVo> queryList(PdProjectCollectQuery query, Integer pageNo, Integer pageSize);

    /**
     * 新增项目
     *
     * @param pdProjectCollect
     * @return
     */
    Boolean insert(PdProjectCollect pdProjectCollect);

    /**
     * 查询项目关联单位信息
     *
     * @param query
     * @return
     */
    Result<Rs<PdProjectEntCollect>> queryEntInfo(ProjectEntQuery query);

    /**
     * 新增项目参建单位
     *
     * @param pdProjectEntCollect
     * @return
     */
    Boolean insertEnt(PdProjectEntCollect pdProjectEntCollect);

    /**
     * 删除项目
     *
     * @param list
     * @return
     */
    Boolean removeProject(List<String> list);
}
