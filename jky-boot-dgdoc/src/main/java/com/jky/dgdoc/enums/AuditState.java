package com.jky.dgdoc.enums;

/**
 * @Author: lpg
 * @Date: 2024/01/08
 * @Description: 审核状态
 */
public enum AuditState {
    /**
     * 未审核
     */
    UNAUDITED("0", "未审核"),
    /**
     * 部分审核
     */
    PARTAUDITED("1", "部分审核"),
    /**
     * 审核通过
     */
    PASS("2", "审核通过"),
    /**
     * 已退回
     */
    RETURN("3", "已退回");

    private String code;
    private String name;

    AuditState(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code) {
        for (AuditState value : AuditState.values()) {
            if (value.getCode().equals(code)) {
                return value.getName();
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
