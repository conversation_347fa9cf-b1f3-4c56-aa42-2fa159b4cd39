package com.jky.dgdoc.enums;

import lombok.Getter;

/**
 * @Author: lpg
 * @Date: 2024/01/09
 * @Description:
 */
@Getter
public enum EntType {
    /**
     * 勘察单位
     */
    SURVEY("1", "勘察单位"),
    /**
     * 设计单位
     */
    DESIGN("2", "设计单位"),
    /**
     * 监理单位
     */
    SUPERVISION("3", "监理单位"),
    /**
     * 施工单位
     */
    CONSTRUCTION("11", "施工单位"),
    /**
     * 建设单位
     */
    CONSTRUCTION_UNIT("18", "建设单位");


    private String code;
    private String name;

    EntType(String code, String name) {
        this.code = code;
        this.name = name;
    }


}
