package com.jky.dgdoc.fegin;

import com.dtflys.forest.annotation.Address;
import com.dtflys.forest.annotation.JSONBody;
import com.dtflys.forest.annotation.Post;
import com.jky.dgdoc.domain.*;
import com.jky.dgdoc.domain.dto.SysUserDto;
import org.jeecg.common.api.vo.Result;

/**
 * @Author: lpg
 * @Date: 2023/12/29
 * @Description: 工地档案外部接口调用
 */
@Address(port = "${port}", basePath = "/jky-boot/out/api")
public interface DgdocApiClient {

    @Post(url = "/getAqjdConsSite")
    Result<Rs<DgdocAqjdConsSite>> getAqjdConsSite(@JSONBody DgdocAqjdConsSite body);

    @Post(url = "/getAqjdConsSiteSub")
    Result<Rs<DgdocAqjdConsSiteSub>> getAqjdConsSiteSub(@JSONBody DgdocAqjdConsSiteSub body);
    /**
     * 1.获取项目信息
     *
     * @param body
     * @return
     */
    @Post(url = "/getProjectCollect")
    Result<Rs<PdProjectCollect>> getProjectCollect(@JSONBody PdProjectCollect body);

    /**
     * 2.获取项目关联单位信息
     *
     * @param body
     * @return
     */
    @Post(url = "/getProjectEntCollect")
    Result<Rs<PdProjectEntCollect>> getProjectEntCollect(@JSONBody PdProjectEntCollect body);

    /**
     * 3.获取企业信息
     * * @description 获取企业信息
     * * 传参说明：
     * * 1.必传：entType，全匹配；
     * * 2.选传：entAddress，corporationRegCode,entName,entId
     * * (字段说明和返回属性，请查看 PdEntInfoCollect.class 和《表结构信息-拟接口用-初稿-20231204》)
     * * 3.只能查询最多返回20条数据
     * * @param pdEntInfoCollect
     * * @date 2023/12/6
     *
     * @param body
     * @return
     */
    @Post(url = "/getEntCollect")
    Result<Rs<PdEntInfoCollect>> getEntCollect(@JSONBody PdEntInfoCollect body);

    /**
     * 4.获取项目单体信息
     * * 传参说明：
     * * 1.必传：projectId，全匹配；
     * * 2.选传：monomerName，monomerId，licencemoney
     * * (字段说明和返回属性，请查看 PdMonomerCollect.class 和《表结构信息-拟接口用-初稿-20231204》)
     * * 3.只能查询最多返回20条数据
     *
     * @param body
     * @return
     */
    @Post(url = "/getMonomerCollect")
    Result<Rs<PdMonomerCollect>> getMonomerCollect(@JSONBody PdMonomerCollect body);

    /**
     * 5.获取项目单体的关联企业信息
     *
     * @param body
     * @return
     * @description 获取项目单体的关联企业信息
     * * 传参说明：
     * * 1.必传：projectId 、monomerId 和  entType，全匹配；
     * * 2.选传：monomerName
     * * (字段说明和返回属性，请查看 PdMonomerEntCollect.class 和《表结构信息-拟接口用-初稿-20231204》)
     * * 3.只能查询最多返回20条数据
     */
    @Post(url = "/getMonomerEntCollect")
    Result<Rs<PdMonomerEntCollect>> getMonomerEntCollect(@JSONBody PdMonomerEntCollect body);

    /**
     * 6.根据企业获取项目信息
     *
     * @param body
     * @return
     */
    @Post(url = "/getProjectByEnt")
    Result<Rs<PdProjectCollect>> getProjectByEnt(@JSONBody PdProjectByEnt body);

    /**
     * 7.获取施工许可信息
     * * 传参说明：
     * * 1.必传：无
     * * 2.选传：dataId (单体表PdMonomerCollect的dataId)
     * * (字段说明和返回属性，请查看 PdConsPermitCollect.class)
     * * 3.只能查询最多返回20条数据
     * * @date 2024/1/18
     * * 传参demo
     * * {
     * * "consPermitId":"8a1288e98b9377dc018ba95d551a0562",
     * * }
     *
     * @param body
     * @return
     */
    @Post(url = "/getConsPermitCollect")
    Result<Rs<PdConsPermitCollect>> getConsPermitCollect(@JSONBody PdConsPermitCollect body);

    /**
     * 添加用户
     *
     * @param body 用户角色资料  必传字段{realname:"建科院(企业名称)" ,"organizationId":"911113213111（组织机构ID）","entType":"1（企业类型）"}
     * @return 返回结果
     * {
     * code: 0
     * message: "success"  message值：1、success 2、新增用户必要参数缺少 3、新增用户异常
     * result: null
     * success: true success 值：1、true（添加成功） 2、false（添加失败）
     * timestamp: 1703664882226 （当前时间戳）
     * }
     * @throws Exception
     */
    @Post(url = "/addDocEnt")
    Result<String> addDocEnt(@JSONBody SysUserDto body);

}
