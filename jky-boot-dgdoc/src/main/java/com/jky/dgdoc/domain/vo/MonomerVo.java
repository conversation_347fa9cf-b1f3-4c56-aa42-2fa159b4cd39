package com.jky.dgdoc.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jky.dgdoc.domain.PdMonomerEntCollect;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: lpg
 * @Date: 2024/01/03
 * @Description:
 */
@Data
public class MonomerVo implements Serializable {
    /**
     * 单体工程信息表ID
     */
    private String monomerId;
    /**
     * 单体工程名称
     */
    private String monomerName;
    /**
     * 项目ID
     * 项目名称
     * 项目代码
     * 宗地代码
     */
    private String projectId;
    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 许可证号
     */
    private String licencemoney;
    /**
     * 施工许可日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date licencedate;
    /**
     * 工规证号
     */
    private String proPlanCerNo;
    /**
     * 是否竣工验收
     */
    private String isFinish;
    /**
     * 竣工验收日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date finishDate;
    /**
     * 是否竣工验收备案
     */
    private String isFinishBackup;
    /**
     * 竣工验收备案证书号
     */
    private String finishBackupNo;
    /**
     * 竣工验收备案日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date finishBackupDate;
    /**
     * 是否终止监督
     */
    private String isStope;
    /**
     * 终止监督日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date stopeDate;
    /**
     * 镇区名称
     */
    private String townshipName;
    /**
     * 单体工程数据来源
     */
    private String dataSource;
    /**
     * 单体工程数据来源表主键
     */
    private String dataId;
    /**
     * 数据状态
     */
    private String dataState;
    /**
     * 工程状态 1-未介入 2-在建 3-停工 4-终止监督
     */
    private String gczt;
    /**
     * 工程类别
     */
    private String gclb;
    /**
     * 楼栋代码
     */
    private String lddm;
    /**
     * 安全监督登记号
     */
    private String ajNum;
    /**
     * 质量监督登记号
     */
    private String zjNum;
    /**
     * 建设同步时间
     */
    private Date jkSyncTime;
    /**
     * 施工许可证附件
     */
    private String licenceFile;
    /**
     * 计划开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date planStartDate;
    /**
     * 计划结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date planEndDate;

    /**
     * 施工单位
     */
    private PdMonomerEntCollect sgUnit;
    /**
     * 设计单位
     */
    private PdMonomerEntCollect sjUnit;
    /**
     * 监理单位
     */
    private PdMonomerEntCollect jlUnit;
    /**
     * 勘察单位
     */
    private PdMonomerEntCollect kcUnit;
}
