package com.jky.dgdoc.domain.query;

import lombok.Data;

import java.io.Serializable;

/**
 * @Author: lpg
 * @Date: 2024/01/08
 * @Description: 档案查询条件
 */
@Data
public class ArchivesQuery implements Serializable {
    /**
     * 项目/单体ID
     */
    private String projectMonomerId;
    /**
     * 档案类型 0-项目档案 1-单体档案
     */
    private String archivesType;
    /**
     * 文件名称
     */
    private String archivesName;

    /**
     * 上传状态 0-未上传 1-部分上传 2-已上传
     */
    private String uploadState;
    /**
     * 审核状态 0-未审核 1-部分审核 2-已审核 3-已退回
     */
    private String auditState;
    /**
     * 状态
     */
    private String status;
}
