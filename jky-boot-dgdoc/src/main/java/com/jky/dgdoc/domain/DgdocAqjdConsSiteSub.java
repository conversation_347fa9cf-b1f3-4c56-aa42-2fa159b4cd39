package com.jky.dgdoc.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 工地表子表（单体工程信息表）实体类
 */
@Data
@TableName("dgdoc_aqjd_cons_site_sub")
public class DgdocAqjdConsSiteSub implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 数据主键
     */
    @TableId
    private String id;

    /**
     * 单体工程名称
     */
    private String monomerName;

    /**
     * 长度（Km）
     */
    private Double distance;

    /**
     * 投资总额（万元）
     */
    private Double investAmount;

    /**
     * 所属镇区ID
     */
    private String townshipId;

    /**
     * 工程状态
     */
    private String projectStatus;

    /**
     * 单体工程信息表ID
     */
    private String monomerId;

    /**
     * 投资类别
     */
    private String investType;

    /**
     * 工程造价（万元）
     */
    private Double projectCost;

    /**
     * 计划工期开始时间
     */
    private Date periodStartTime;

    /**
     * 基础类型
     */
    private String baseType;

    /**
     * 监督办理
     */
    private String supDeal;

    /**
     * 地上层数（层）
     */
    private Long onFloors;

    /**
     * 安全监督注册号（监督登记号）
     */
    private String ssRegNo;

    /**
     * 删除标记
     */
    private String signDeleted;

    /**
     * 工程类别
     */
    private String projectType;

    /**
     * 地下层数（层）
     */
    private Long underFloors;

    /**
     * 工地ID（项目ID）
     */
    private String consSiteId;

    /**
     * 工程地点
     */
    private String projectAddress;

    /**
     * 面积（m2）
     */
    private Double projectSize;

    /**
     * 所属镇区
     */
    private String townshipName;

    /**
     * 跨度（m）
     */
    private Double spanLength;

    /**
     * 施工许可证号
     */
    private String licencemoney;

    /**
     * 监督类型
     */
    private String supType;

    /**
     * 结构形式
     */
    private String consForm;

    /**
     * 施工许可日期
     */
    private Date licencedate;

    /**
     * 计划工期结束时间
     */
    private Date planEndTime;

    /**
     * 最后更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatetime;

    /**
     * 数据全字段MD5
     */
    private String dataMd5;

    /**
     * 数据标识
     */
    private String zzDataFlag;

    /**
     * 数据同步时间（自动更新）
     */
    private Date jkSyncTime;

}