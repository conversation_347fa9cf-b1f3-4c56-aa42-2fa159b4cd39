package com.jky.dgdoc.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: lpg
 * @Date: 2024/01/04
 * @Description: 档案实例
 */
@Data
@TableName("dgdoc_archives_instance")
public class DgdocArchivesInstance implements Serializable {

    /**
     * 档案实例ID
     */
    @TableId
    private String archivesInstanceId;
    /**
     * 项目/单体ID
     */
    private String projectMonomerId;
    /**
     * 档案ID
     */
    private String archivesId;
    /**
     * 档案父级ID
     */
    private String parentId;
    /**
     * 档案编号
     */
    private String archivesNo;
    /**
     * 档案名称
     */
    private String archivesName;
    /**
     * 档案类型 0-项目档案 1-单体档案
     */
    private String archivesType;
    /**
     * 文件类型
     */
    private String fileType;
    /**
     * 文件名称
     */
    private String fileUrl;
    /**
     * 文件大小
     */
    private BigDecimal fileSize;
    /**
     * 文件页数
     */
    private Integer filePage;
    /**
     * 上传日期
     */
    private Date uploadDate;
    /**
     * 上传状态 0-未上传 1-部分上传 2-已上传
     */
    private String uploadState;
    /**
     * 文件状态 0-未归档 1-部分归档 2-已归档
     */
    private String auditState;
    /**
     * 状态 0-正常 1-作废
     */
    private String status;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 退回原因
     */
    private String returnBackReason;
    /**
     * 原因备注
     */
    private String returnBackRemark;
    /**
     * 参建方确认附件
     */
    private String cjEntConfirmFile;
}
