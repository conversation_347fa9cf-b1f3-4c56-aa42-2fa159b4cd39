package com.jky.dgdoc.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.jeecg.common.aspect.annotation.Dict;

import java.io.Serializable;
import java.util.Date;

/**
 * 1.0.5单体工程信息集成对象 pd_monomer_collect
 */
@Data
@TableName("dgdoc_monomer")
public class PdMonomerCollect implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 单体工程信息表ID
     */
    @TableId
    private String monomerId;
    /**
     * 单体工程名称
     */
    private String monomerName;
    /**
     * 项目ID
     * 项目名称
     * 项目代码
     * 宗地代码
     */
    private String projectId;
    /**
     * 项目名称
     */
    @TableField(exist = false)
    private String projectName;
    /**
     * 许可证号
     */
    private String licencemoney;
    /**
     * 施工许可日期
     */
    private Date licencedate;
    /**
     * 工规证号
     */
    private String proPlanCerNo;
    /**
     * 是否竣工验收
     */
    private String isFinish;
    /**
     * 竣工验收日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date finishDate;
    /**
     * 是否竣工验收备案
     */
    private String isFinishBackup;
    /**
     * 竣工验收备案证书号
     */
    private String finishBackupNo;
    /**
     * 竣工验收备案日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date finishBackupDate;
    /**
     * 是否终止监督
     */
    private String isStope;
    /**
     * 终止监督日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date stopeDate;
    /**
     * 镇区名称
     */
    private String townshipName;
    /**
     * 单体工程数据来源
     */
    private String dataSource;
    /**
     * 单体工程数据来源表主键
     */
    private String dataId;
    /**
     * 数据状态
     */
    private String dataState;
    /**
     * 工程状态 1-未介入 2-在建 3-停工 4-终止监督
     */
    private String gczt;
    /**
     * 工程类别
     */
    @Dict(dicCode = "dgzjjsp_project_type")
    private String gclb;
    /**
     * 楼栋代码
     */
    private String lddm;
    /**
     * 安全监督登记号
     */
    private String ajNum;
    /**
     * 质量监督登记号
     */
    private String zjNum;
    /**
     * 建设同步时间
     */
    private Date jkSyncTime;
    /**
     * 创建者
     */
    private String createBy;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 施工许可证附件
     */
    private String licenceFile;
    /**
     * 建设工程规划许可证证号
     */
    private String gcghxkNo;
    /**
     * 计划开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date planStartDate;
    /**
     * 计划结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date planEndDate;

    /**
     * 是否虚拟单体
     */
    private Boolean isVirtual = false;
}
