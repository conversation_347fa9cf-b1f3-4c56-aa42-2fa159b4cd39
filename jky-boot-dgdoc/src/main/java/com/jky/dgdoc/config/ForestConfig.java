package com.jky.dgdoc.config;

import com.dtflys.forest.Forest;
import com.dtflys.forest.config.ForestConfiguration;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @Author: lpg
 * @Date: 2024/01/12
 * @Description:
 */
@Configuration
public class ForestConfig {

    @Value("${server.port}")
    private String port;

    @Bean
    public void init() {
        // 获取 Forest 全局配置对象
        ForestConfiguration configuration = Forest.config();
        // 设置全局变量: name -> Peter
        configuration.setVariableValue("port", port);
    }

}
