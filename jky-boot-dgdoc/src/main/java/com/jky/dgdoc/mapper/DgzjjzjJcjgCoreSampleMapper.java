package com.jky.dgdoc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jky.dgdoc.domain.DgzjjzjJcjgCoreSample;
import com.jky.dgdoc.domain.vo.DgzjjzjJcjgCoreSampleTableVo;
import com.jky.dgdoc.domain.vo.DgzjjzjSupJcjgUnqualifiedReplyFileVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 单体检测报告Mapper接口
 */
public interface DgzjjzjJcjgCoreSampleMapper extends BaseMapper<DgzjjzjJcjgCoreSample> {
    /**
     * 查询单体检测报告列表
     *
     * @param monomerId
     * @return
     */
    List<DgzjjzjJcjgCoreSampleTableVo> queryMonomerJcjgList(@Param("monomerId") String monomerId);

    /**
     * 查询单体是否存在不合格的检测报告
     */
    String queryMonomerJcjgUnqualified(@Param("monomerId") String monomerId);

    /**
     * 根据检测编号查询销案过程
     */
    List<DgzjjzjSupJcjgUnqualifiedReplyFileVo> queryMonomerJcjgByJcbh(@Param("synum") String synum);
}
