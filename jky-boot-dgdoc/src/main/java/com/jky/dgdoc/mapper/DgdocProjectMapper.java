package com.jky.dgdoc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jky.dgdoc.domain.PdProjectCollect;
import com.jky.dgdoc.domain.query.PdProjectCollectQuery;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Set;

public interface DgdocProjectMapper extends BaseMapper<PdProjectCollect> {
    @Select("select role_code from sys_role where id in (select role_id from sys_user_role where user_id = (select id from sys_user where username=#{username}))")
    Set<String> getUserRolesSet(@Param("username") String username);

    @Select("select organization_id from sys_user where id=#{userId}")
    String getUserOrganizationId(@Param("userId") String userId);

    @Select("select dep_id from sys_user_depart where user_id = #{userId}")
    List<String> getUserDepId(@Param("userId") String userId);

    /**
     * 查询项目列表
     *
     * @param page
     * @param ew
     * @return
     */
    IPage<PdProjectCollect> pageList(Page<PdProjectCollect> page, @Param(Constants.WRAPPER) PdProjectCollectQuery ew, @Param("creditCode") String creditCode);
}
