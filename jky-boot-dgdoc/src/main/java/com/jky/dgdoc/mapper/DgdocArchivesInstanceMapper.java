package com.jky.dgdoc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.jky.dgdoc.domain.DgdocArchivesInstance;
import com.jky.dgdoc.domain.query.ArchivesQuery;
import com.jky.dgdoc.domain.vo.DgZjjgMonomerfileVo;
import com.jky.dgdoc.domain.vo.DgdocArchivesInstanceVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 档案实例Mapper接口
 */
public interface DgdocArchivesInstanceMapper extends BaseMapper<DgdocArchivesInstance> {

    /**
     * 查询档案实例列表
     *
     * @param archivesQuery
     * @return
     */
    List<DgdocArchivesInstanceVo> queryList(@Param(Constants.WRAPPER) ArchivesQuery archivesQuery);

    /**
     * 通过档案实例ID查询附件
     */
    List<DgZjjgMonomerfileVo> queryFileList(@Param("archivesInstanceId") String archivesInstanceId);
}
