<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.system.mapper.SysDepartMapper">

	<select id="queryUserDeparts" parameterType="String" resultType="org.jeecg.modules.system.entity.SysDepart">
	   select * from sys_depart where id IN ( select dep_id from sys_user_depart where user_id = #{userId} )
	</select>

    <!-- 根据username查询所拥有的部门 -->
    <select id="queryDepartsByUsername" parameterType="String" resultType="org.jeecg.modules.system.entity.SysDepart">
        SELECT *
        FROM sys_depart
        WHERE id IN (
            SELECT dep_id
            FROM sys_user_depart
            WHERE user_id = (
                SELECT id
                FROM sys_user
                WHERE username = #{username}
            )
        )
    </select>

    <!-- 根据部门Id查询,当前和下级所有部门IDS -->
    <select id="getSubDepIdsByDepId" resultType="java.lang.String">
		select id from sys_depart where del_flag = '0' and org_code like concat((select org_code from sys_depart where id=#{departId}),'%')
	</select>

    <!--根据部门编码获取我的部门下所有部门ids -->
    <select id="getSubDepIdsByOrgCodes" resultType="java.lang.String">
		select id from sys_depart where del_flag = '0' and
        <foreach collection="orgCodes" item="item" index="index"  open="(" separator="or" close=")">
            org_code LIKE CONCAT(#{item},'%')
        </foreach>
	</select>
     <!--根据parent_id查询下级部门-->
    <select id="queryTreeListByPid" parameterType="Object"  resultType="org.jeecg.modules.system.entity.SysDepart">
        SELECT * FROM  sys_depart where del_flag = '0'
        <choose>
            <when test="parentId != null and parentId != ''">
                AND parent_id =  #{parentId,jdbcType=VARCHAR}
            </when>
            <otherwise>
                AND parent_id is null or parent_id=''
            </otherwise>
        </choose>
        order by depart_order
    </select>

    <!-- 根据OrgCod查询公司信息 -->
    <select id="queryCompByOrgCode" resultType="org.jeecg.modules.system.entity.SysDepart">
		select * from sys_depart where del_flag = '0' and org_category='1' and org_code= #{orgCode,jdbcType=VARCHAR}
	</select>
</mapper>