package org.jeecg.modules.regform.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.jeecg.modules.regform.domain.RegFile;
import org.jeecg.modules.regform.domain.RegProjectInfo;
import org.jeecg.modules.regform.mapper.RegFileMapper;
import org.jeecg.modules.regform.mapper.RegProjectInfoMapper;
import org.jeecg.modules.regform.service.RegFileService;
import org.jeecg.modules.regform.service.RegProjectInfoService;
import org.springframework.stereotype.Service;

/**
 * @Author: lpg
 * @Date: 2024/01/02/16:49
 * @Description:
 */
@Service
@RequiredArgsConstructor
public class RegFileServiceImpl extends ServiceImpl<RegFileMapper, RegFile> implements RegFileService {

}
