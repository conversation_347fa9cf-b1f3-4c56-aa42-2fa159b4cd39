package org.jeecg.modules.regform.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.jeecg.modules.regform.domain.RegEntInfo;
import org.jeecg.modules.regform.mapper.RegEntInfoMapper;
import org.jeecg.modules.regform.service.RegEntInfoService;
import org.springframework.stereotype.Service;

/**
 * @Author: lpg
 * @Date: 2024/01/02/16:49
 * @Description:
 */
@Service
@RequiredArgsConstructor
public class RegEntInfoServiceImpl extends ServiceImpl<RegEntInfoMapper, RegEntInfo> implements RegEntInfoService {

}
