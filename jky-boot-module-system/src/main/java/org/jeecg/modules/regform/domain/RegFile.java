package org.jeecg.modules.regform.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("reg_file")
public class RegFile  implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * 数据主键
     */
    @TableId
    private String id;

    /**
     * 文件类型
     */
    private String type;

    /**
     * 表id
     */
    private String tableId;

    /**
     *
     */
    private String fileName;

    /**
     *
     */
    private String newFileName;

    /**
     *
     */
    private String originalFileName;
    /**
     *
     */
    private String url;

    /**
     *
     */
    private String delFlag;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建时间
     */
    private Date updateTime;
}
