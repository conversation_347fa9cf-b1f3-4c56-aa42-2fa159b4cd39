package org.jeecg.modules.regform.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.jeecg.modules.regform.domain.RegFile;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class RegProjectInfoDto implements Serializable {

    /**
     * 项目ID
     */
    private String id;
    /**
     * 项目ID
     */
    private String projectId;
    /**
     * 项目编号/项目代码
     */
    private String projectNo;
    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 项目所在镇（街）ID
     */
    private String projectTownId;
    /**
     * 项目所在镇（街）
     */
    private String projectTown;
    /**
     * 项目详细地址
     */
    private String projectAddress;
    /**
     * 建设单位ID
     */
    private String consUnitId;
    /**
     * 项目类型
     */
    private String dataType;
    /**
     * 是否装配式建筑
     */
    private String isBuild;
    /**
     * 宗地代码
     */
    private String landNo;
    /**
     * 行政区
     */
    private String xzq;
    /**
     * 项目坐落
     */
    private String xmzl;
    /**
     * 项目总投资（万元）
     */
    private Long xmztz;
    /**
     * 建设单位
     */
    private String consUnit;
    /**
     * 土地面积（平方米）
     */
    private Long tdmj;
    /**
     * 总建筑面积（平方米）
     */
    private Long zjzmj;
    /**
     * 计划开工日期
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date jhkgrq;
    /**
     * 计划竣工日期
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date jhjgrq;
    /**
     * 数据状态
     */
    private String dataState;
    /**
     * 土地用途
     */
    private String tdyt;
    /**
     * 建设性质
     */
    private String jsxz;
    /**
     * 建设类别
     */
    private String jslb;
    /**
     * 容积率
     */
    private Long rjl;
    /**
     * 绿地率
     */
    private Long ldl;
    /**
     * 建设规模及内容
     */
    private String jsgmjnr;
    /**
     * 项目状态
     */
    private String projectState;
    /**
     * 是否重大项目 1.是  0.否
     */
    private String isMajorProject;

    /**
     * 单位名称
     */
    private String decUnitName;

    /**
     * 单位组织机构代码
     */
    private String decUnitCode;

    /**
     * 单位地址
     */
    private String decUnitAddress;

    /**
     * 单位联系姓名
     */
    private String decUnitContacts;

    /**
     * 单位联系电话
     */
    private String decUnitTel;

    /**
     * 单位联系邮件
     */
    private String decUnitEmail;


    /**
     * 创建者
     */
    private String createBy;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建时间
     */
    private Date updateTime;
    /**
     * 项目经理姓名
     */
    private String managerName;
    /**
     * 项目经理电话
     */
    private String managerTel;
    /**
     * 退回原因
     */
    private String returnBackReason;
    /**
     * 审核 状态，0可修改勤劳退回，2提交成功，3通过
     */
    private String status;

//    /**
//     * 企业社会信用代码(查询项目时过滤企业)
//     */
//    private String organizationId;

    /**
     * 上传附件列表
     */
    private List<RegFile> regFileList;


    /**
     * 对比数据
     */

    /**
     * 项目ID
     */
    private String id2;
    /**
     * 项目ID
     */
    private String projectId2;
    /**
     * 项目编号/项目代码
     */
    private String projectNo2;
    /**
     * 项目名称
     */
    private String projectName2;
    /**
     * 项目所在镇（街）ID
     */
    private String projectTownId2;
    /**
     * 项目所在镇（街）
     */
    private String projectTown2;
    /**
     * 项目详细地址
     */
    private String projectAddress2;
    /**
     * 建设单位ID
     */
    private String consUnitId2;
    /**
     * 项目类型
     */
    private String dataType2;
    /**
     * 是否装配式建筑
     */
    private String isBuild2;
    /**
     * 宗地代码
     */
    private String landNo2;
    /**
     * 行政区
     */
    private String xzq2;
    /**
     * 项目坐落
     */
    private String xmzl2;
    /**
     * 项目总投资（万元）
     */
    private Long xmztz2;
    /**
     * 建设单位
     */
    private String consUnit2;
    /**
     * 土地面积（平方米）
     */
    private Long tdmj2;
    /**
     * 总建筑面积（平方米）
     */
    private Long zjzmj2;
    /**
     * 计划开工日期
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date jhkgrq2;
    /**
     * 计划竣工日期
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date jhjgrq2;
    /**
     * 数据状态
     */
    private String dataState2;
    /**
     * 土地用途
     */
    private String tdyt2;
    /**
     * 建设性质
     */
    private String jsxz2;
    /**
     * 建设类别
     */
    private String jslb2;
    /**
     * 容积率
     */
    private Long rjl2;
    /**
     * 绿地率
     */
    private Long ldl2;
    /**
     * 建设规模及内容
     */
    private String jsgmjnr2;
    /**
     * 项目状态
     */
    private String projectState2;
    /**
     * 是否重大项目 1.是  0.否
     */
    private String isMajorProject2;

    /**
     * 单位名称
     */
    private String decUnitName2;

    /**
     * 单位组织机构代码
     */
    private String decUnitCode2;

    /**
     * 单位地址
     */
    private String decUnitAddress2;

    /**
     * 单位联系姓名
     */
    private String decUnitContacts2;

    /**
     * 单位联系电话
     */
    private String decUnitTel2;

    /**
     * 单位联系邮件
     */
    private String decUnitEmail2;


    /**
     * 创建者
     */
    private String createBy2;

    /**
     * 更新者
     */
    private String updateBy2;

    /**
     * 创建时间
     */
    private Date createTime2;

    /**
     * 创建时间
     */
    private Date updateTime2;
    /**
     * 项目经理姓名
     */
    private String managerName2;
    /**
     * 项目经理电话
     */
    private String managerTel2;
    /**
     * 退回原因
     */
    private String returnBackReason2;
    /**
     * 审核 状态，0可修改勤劳退回，2提交成功，3通过
     */
    private String status2;

//    /**
//     * 企业社会信用代码(查询项目时过滤企业)
//     */
//    private String organizationId2;

    /**
     * 上传附件列表
     */
    private List<RegFile> regFileList2;


//    /**
//     * 数据主键
//     */
//    private String buildProjectEntId;
//    /**
//     * 项目id
//     */
//    private String buildProjectId;
//    /**
//     * 企业id
//     */
//    private String buildEntId;
//    /**
//     * 企业名称
//     */
//    private String buildEntName;
//    /**
//     * 企业统一社会信用代码
//     */
//    private String buildCreditCode;
//    /**
//     * 企业资质证书编号
//     */
//    private String buildAptitudeCerNo;
//    /**
//     * 项目负责人
//     */
//    private String buildProLeader;
//    /**
//     * 负责人联系电话
//     */
//    private String buildProLeaderMobile;

}
