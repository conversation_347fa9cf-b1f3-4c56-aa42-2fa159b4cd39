package org.jeecg.modules.api.utils;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import cn.hutool.crypto.symmetric.AES;
import lombok.extern.slf4j.Slf4j;

import java.security.KeyPair;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class RsaCommonUtil {

    //私钥
    //工地档案私钥
    public static String privateKeyForGddoc="MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBAKLNtO5BFo7xxYTJFmkCnpFYarKuoRQBClXvVng+T6t2FGc+9ANFjVe9no4H9e4p+qpVJoZMjH5T2ZetoAuA2GT39142CfJwayg1czz/ZzT8+EedfEMiccCKvZoLs9D+llRNawvm0icEsnDp+TN493OuWXm1OpPQDeIZpAU4rbBXAgMBAAECgYAZ/dt3+j4+vgy5wVzpkPh93e7S7hxbVDsxMw/MDK6tgiGJxjww1YbWTjilFlJGHr+dOBD5efkoJfzkQVz+2Sgy3Dkpv7hxkVINSezT76YbHeiE3V1NN/itPtlcT2h0o9NimFXZwY1YC12WnsYin3Aiq8M+ATgdxEe7D5uWcSZRkQJBAPUKUhUQr3EHar7Lh+G0zD2ZhuXwMOXJW2XPDRn3bATMYT5xrSWWVJUt8oyw3V6PvKNtaYZ7RfkCwoxXl5wRnZ8CQQCqFckwgXBdkrPAJtFdKemdo4kbH9z3GaX4KqNWYAeaJWW+MnMmjL6sWhekcGdjLSmTc3hggrMAhxDKOvtbQ4JJAkA/GjpLHHHhrKRLoARNFo8mKdw4aKNe+Xyk+i4IitPxZGSCfSm2rUD47c0xd/Z5/I/6khmDvUkmuBQ7qe7sDQlHAkBByX1xMo/I0caPCzSt6VlALRIKGM/6D8SfcbaN4UTvfYmWAKODhKr3tT0x0Hxc4woSy3YDNmqQSK3Y2umASmNpAkAYhE2446ExQCd3AS6+zpmMI/8jgyiuEdZvpfb3+OQR/moLEQlx3642cl1KRQm89tanlyULj9hE+qpv7pMtpCzG";

    // 密码盐
    public static String getSalt() {
        return SecureUtil.md5(IdUtil.simpleUUID());
    }

    // 系统密码
    public static String getPassword(String unencryptedPassword, String salt, String username) {
        return SecureUtil.md5(SecureUtil.sha256(unencryptedPassword) + salt + username);
    }

    // 校验密码强度
    public static boolean verifyPasswordStrength(String password) {
        // 密码8-20位，需同时包含数字、特殊字符（英文状态）!@#$%^&*()以及字母
        String regex = "^(?=.*\\d)(?=.*[a-zA-Z])(?=.*[!@#\\$%\\^&\\*\\(\\)])[a-zA-Z0-9!@#\\$%\\^&\\*\\(\\)]{8,20}$";
        return ReUtil.isMatch(regex, password);
    }

    // 生成rsa秘钥对
    public static Map<String, String> generateRSAKeyPair() {
        KeyPair keyPair = SecureUtil.generateKeyPair("RSA");
        String privateKey = Base64.encode(keyPair.getPrivate().getEncoded());
        String publicKey = Base64.encode(keyPair.getPublic().getEncoded());
        Map<String, String> pair = new HashMap<>();
        pair.put("privateKey", privateKey);
        pair.put("publicKey", publicKey);
        System.out.println("privateKey:>>>>>>>>> "+pair.get("privateKey"));
        System.out.println("publicKey:>>>>>>>>> "+pair.get("publicKey"));
        return pair;
    }

    // 实例化AES
    public static AES initAES(String key) {
        return new AES(Mode.CBC, Padding.PKCS5Padding, key.getBytes(), key.getBytes());
    }

    // 使用公钥实例化RSA
    public static RSA initRSAByPublicKey(String publicKey) {
        return new RSA(null, publicKey);
    }

    // 使用私钥实例化RSA
    public static RSA initRSAByPrivateKey(String PrivateKey) {
        return new RSA(PrivateKey, null);
    }

    // AES加密
    public static String AESEncryptBase64(String content, String key) {
        AES aes = initAES(key);
        return aes.encryptBase64(content);
    }

    // AES解密
    public static String AESDecryptBase64(String content, String key) {
        AES aes = initAES(key);
        return aes.decryptStr(content, CharsetUtil.CHARSET_UTF_8);
    }

    // RSA 公钥加密
    public static String RSAEncryptBase64ByPublicKey(String content, String publicKey) {
        RSA rsa = initRSAByPublicKey(publicKey);
        return rsa.encryptBase64(content, KeyType.PublicKey);
    }

    // RSA 公钥解密
    public static String RSADecryptBase64ByPublicKey(String content, String publicKey) {
        RSA rsa = initRSAByPublicKey(publicKey);
        return rsa.decryptStr(content, KeyType.PublicKey);
    }

    // RSA 私钥加密
    public static String RSAEncryptBase64ByPrivateKey(String content, String privateKey) {
        RSA rsa = initRSAByPrivateKey(privateKey);
        return rsa.encryptBase64(content, KeyType.PrivateKey);
    }

    // RSA 私钥解密
    public static String RSADecryptBase64ByPrivateKey(String content, String privateKey) {
        RSA rsa = initRSAByPrivateKey(privateKey);
        return rsa.decryptStr(content, KeyType.PrivateKey);
    }

    // MD5 加密
    public static String md5f32(String plainText) throws NoSuchAlgorithmException {
        MessageDigest md = MessageDigest.getInstance("MD5");
        md.update(plainText.getBytes());
        byte b[] = md.digest();
        int i;
        StringBuffer buf = new StringBuffer("");
        for (int offset = 0; offset < b.length; offset++) {
            i = b[offset];
            if (i < 0){
                i += 256;
            }
            if (i < 16){
                buf.append("0");
            }
            buf.append(Integer.toHexString(i));
        }
        return buf.toString();
    }

    public static void main(String[] args) {
//        String privateKey = "MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBALHveLjR7DDPabUfrKrVrI43cSuuu/nqrdHO3GkMCgDbQHUroEzXekRidsZol5K7LVHjEI6zfbdX1hbwJcwdbIZGTWuQuw6wnfM+YnOhBhiBiOk6/WYpGLx0UnFc8cFbp7+EIMlpuHAZapHNHF7PEXu6m/sF6L+MUkMjZUSj1AP/AgMBAAECgYBOWQyGDOD8KQf5mcXUvxsoz97EBj987sm0nWpN0uBAuknqjdganbKXk+eKu5kqIcL10I6E1yHe7g01sm5iLv6OcH0GCgVlU5eD0KEJWIcRZPmh/eYWLQCVvI8JRuEfB5v2EcdPDO5/mxHEkjRWr6dSObc9gBqi3C04h03+sEr/kQJBAOnyxf/AIPkXgBvacQklwLwJgarzioc9OScI5Yq/YS8qAf2VdA1ncPmR3hTHGBeopxXk5U6122kcBnvnZcq7+9cCQQDCtRgai0egwP82QOpemW9cJn8RJG1KM7SCiPgB+Wegy14Jb/nMJwM+mZ8GAuOfrbn50pWDUWrwZ7A3aBmuHXQZAkEAhSWRwLvlSD8yvyd0m8MrzRkP/m57UfrrMyciFPRU6FXdKijMEvDjYHa/UU36jUSES7apN0HTLSAxJIWO7nJMowJAVCxN20vTD1/f9OhjaboTNlc+H38L8EQiCTyFWvUjI5RHdvT9nJBRCsMGyGZ/WQyVlbbABCEyhfONbqaGU4tQIQJBAMWb9r+ig8sdG91ji/+HHC1/z4lRvviQj4vl4Wharf2QOUs4GSxw6CPgBmbC+P9CuRdn4lQ2xnHtA7e2qrwLo5Y=";
//        String result ="IYr+9TjqzsALHhst09B/0lOOwHdoXNYJAe39Z//bLVk1gAJWJKflQjLXpBg9wjtydPtp7x75bgVNVlqsbEG5xpfzwKmt3LklpBvtXEdQiw6WKHmSM0j9xPWtmXi1uOUNZc265QVJ+RoGZp4LY+yxfMqY1r+c4e5zKAXG2JlL/EWrjhBjj0lSL9hIMixEaCzvdhJgs2c1RXhwF6tLx9nJnPvwucU9VHCtAxNhH2kD/6hhIX1e2/etOXoT3DCwfuBp8dXKlZBvMT7sFA0ejvyEIKfQQA97LizeUXya183tpfI73QknLsBi4R08ioUKPAMBwpjU8Z7Icji4R14OMPJwFWHUJTQR0LC6/0Cf3tG33AZZeBNjVts8gz/8TOimRcxN+/fEQtuYokNEtPEVkmck9Occ0E7a05Hv+edm0l59h/9UMOg+iYX++pqsWOauhEhXVWQy8jed20HuVdRG8L8K3BzPEJmZc+YnwdhhISz5sEsK+C3MnugArpbNTq9hIcIHC/Lu6NYU1a/HCjEcyLoSv1t8o4+y/UG7/u4xggfJP9ZDevli+IgNAzDer0zeuL1fmIoc+eI98a/N4VGmY1VbJDW6UTBLV3x8q8rEuvwwhcJDNWF2p9xh1O1jrzOY9mDcJX8db5ZWu7wgLFO9vdPJvHh1AiurG2xkpovuqOnyI6FpIz+zPbYqtJEFQD9SYRcz4UuXBotSc8ZQmkOxKJPpEu4FDyvfzQsg/y99MdQcJ+xNCLsQcfUnSTndPK50qeWOY/mqAbaQrpBwW69x3FdF3/AZHekOS3tJa69Li9gNzXxPQNfPMzovSOHUXKQBmiZv6YBLQAAJgo36SMmgtvh1k4E/6Zpt+4lDwOfMif3jZ7sxgVieMyeHGxGgtTe5kVH7V7GqxFwQ8V4FI9gIqRjRgo6UY78u+orONoHPBAcPEfCySw26veQJud37kzB/FsV69XUlGYMnyVFy/OamXSibg89jXE4GrjFu3Jqp57SS5gFCZrn19rFINUy1n03PQ7lQBRkAP8Enablrnx7IlR6Yp31IukP06EM5uWB3e1Vkxmx5dEN5nQ8xHiF8HsViEWGk9NJouPasF+iFSVb1Uc0Z3AOm1/soq7Xhg6hxGp0hvc6lwN+hkE2ru/gzeeprwNXnxlLoJFNeR6iULdruxBlpiV8LZblQZ7UDbA1//ljZTpY=";
//        try {
//            System.out.println("md5值"+md5f32(result));
//        } catch (NoSuchAlgorithmException e) {
//            e.printStackTrace();
//        }
//        System.out.println("解密后参数："+RsaCommonUtil.RSADecryptBase64ByPrivateKey(result, privateKey));

        RsaCommonUtil.generateRSAKeyPair();
    }
}
