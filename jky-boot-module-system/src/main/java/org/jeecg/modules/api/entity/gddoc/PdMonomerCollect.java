package org.jeecg.modules.api.entity.gddoc;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.util.Date;

/**
 * 1.0.5单体工程信息集成对象 pd_monomer_collect
 *
 */
@Data
public class PdMonomerCollect  {

    private static final long serialVersionUID=1L;

    /**
     * 单体工程信息表ID
     */
    private String monomerId;
    /**
     * 单体工程名称
     */
    private String monomerName;
    /**
     * 项目ID
项目名称
项目代码
宗地代码
     */
    private String projectId;
    /**
     * 许可证号
     */
    private String licencemoney;
    /**
     * 施工许可日期
     */
    private Date licencedate;
    /**
     * 工规证号
     */
    private String proPlanCerNo;
    /**
     * 是否竣工验收
     */
    private String isFinish;
    /**
     * 竣工验收日期
     */
    private Date finishDate;
    /**
     * 是否竣工验收备案
     */
    private String isFinishBackup;
    /**
     * 竣工验收备案证书号
     */
    private String finishBackupNo;
    /**
     * 竣工验收备案日期
     */
    private Date finishBackupDate;
    /**
     * 是否终止监督
     */
    private String isStope;
    /**
     * 终止监督日期
     */
    private Date stopeDate;
    /**
     * 镇区名称
     */
    private String townshipName;
    /**
     * 单体工程数据来源
     */
    private String dataSource;
    /**
     * 单体工程数据来源表主键
     */
    private String dataId;
    /**
     * 数据状态
     */
    private String dataState;
    /**
     * 工程状态
     */
    private String gczt;
    /**
     * 工程类别
     */
    private String gclb;
    /**
     * 楼栋代码
     */
    private String lddm;
    /**
     * 安全监督登记号
     */
    private String ajNum;
    /**
     * 质量监督登记号
     */
    private String zjNum;
    /**
     *
     */
    private Date jkSyncTime;
    /**
     * 创建者
     */
    @TableField(exist = false)
    private String createBy;

    /**
     * 更新者
     */
    @TableField(exist = false)
    private String updateBy;

    /**
     * 创建时间
     */
    @TableField(exist = false)
    private Date createTime;

}
